# -*- coding: utf-8 -*-
"""
图像处理模块
负责图像截取、预处理和调试图像保存功能
从 fund_ocr.py 中分离出来的图像处理相关功能
"""

import cv2
import numpy as np
from typing import List, Tuple, Optional
import mss
import logging
import os
import traceback
from datetime import datetime


class ImageProcessor:
    """图像处理器类，负责截图、预处理和调试保存功能"""
    
    def __init__(self, debug_mode: bool = False, save_debug_images: bool = False):
        """
        初始化图像处理器
        
        Args:
            debug_mode: 是否启用调试模式
            save_debug_images: 是否保存调试图像
        """
        self.logger = logging.getLogger(__name__)
        self.debug_mode = debug_mode
        self.save_debug_images = save_debug_images
        
        # 创建调试目录
        if self.save_debug_images:
            self.debug_dir = f"debug_images_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            os.makedirs(self.debug_dir, exist_ok=True)
    
    def capture_region(self, x: int, y: int, width: int, height: int) -> Optional[np.ndarray]:
        """
        截取屏幕指定区域
        
        Args:
            x, y: 区域左上角坐标
            width, height: 区域尺寸
            
        Returns:
            图像数组或None
        """
        try:
            with mss.mss() as sct:
                # 定义截图区域
                monitor = {
                    "top": y,
                    "left": x,
                    "width": width,
                    "height": height
                }
                
                # 截图
                screenshot = sct.grab(monitor)
                
                # 转换为numpy数组
                img_array = np.array(screenshot)
                
                # 转换颜色格式 (BGRA -> BGR)
                img_bgr = cv2.cvtColor(img_array, cv2.COLOR_BGRA2BGR)
                
                return img_bgr
                
        except Exception as e:
            self.logger.error(f"截取区域失败: {str(e)}")
            return None
    
    def preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        图像预处理以提高OCR识别率
        
        Args:
            image: 原始图像
            
        Returns:
            处理后的图像
        """
        try:
            if self.debug_mode:
                self.logger.debug("开始图像预处理...")
            
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            if self.debug_mode:
                self.logger.debug("已转换为灰度图")
            
            # 调整亮度和对比度
            gray = cv2.convertScaleAbs(gray, alpha=1.5, beta=30)
            
            if self.debug_mode:
                self.logger.debug("已调整亮度和对比度")
            
            # 应用高斯滤波去噪
            denoised = cv2.GaussianBlur(gray, (3, 3), 0)
            
            if self.debug_mode:
                self.logger.debug("已应用高斯滤波")
            
            # 自适应阈值处理
            binary = cv2.adaptiveThreshold(
                denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                cv2.THRESH_BINARY, 11, 2
            )
            
            if self.debug_mode:
                self.logger.debug("已完成自适应阈值处理")
            
            # 形态学操作以改善字符连接
            kernel = np.ones((2, 2), np.uint8)
            processed = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
            
            if self.debug_mode:
                self.logger.debug("图像预处理完成")
            
            return processed
            
        except Exception as e:
            self.logger.error(f"图像预处理失败: {str(e)}")
            if self.debug_mode:
                self.logger.debug(f"预处理错误详情: {traceback.format_exc()}")
            return image

    def preprocess_for_percentage(self, image: np.ndarray) -> np.ndarray:
        """
        专门针对百分比识别的预处理方法
        保护负号、小数点、百分号等特殊字符
        
        Args:
            image: 原始图像
            
        Returns:
            处理后的图像
        """
        try:
            if self.debug_mode:
                self.logger.debug("开始百分比专用预处理...")
            
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 图像尺寸放大（提高小字符识别率）
            scale_factor = 2.0
            height, width = gray.shape
            new_height, new_width = int(height * scale_factor), int(width * scale_factor)
            scaled = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
            
            # 非常轻微的对比度增强（避免字符变形）
            enhanced = cv2.convertScaleAbs(scaled, alpha=1.1, beta=5)
            
            # 应用双边滤波去噪（保护边缘）
            denoised = cv2.bilateralFilter(enhanced, 5, 25, 25)
            
            # 使用OTSU阈值（自动选择最佳阈值）
            _, binary = cv2.threshold(denoised, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            if self.debug_mode:
                self.logger.debug("百分比专用预处理完成")
            
            return binary
            
        except Exception as e:
            self.logger.error(f"百分比预处理失败: {str(e)}")
            return image
    
    def preprocess_for_percentage_v2(self, image: np.ndarray) -> np.ndarray:
        """
        百分比预处理方法 v2 - 保守策略
        最小化处理，保护字符完整性

        Args:
            image: 原始图像

        Returns:
            处理后的图像
        """
        try:
            if self.debug_mode:
                self.logger.debug("开始百分比预处理v2（保守策略）...")

            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # 轻微缩放提升分辨率
            scale_factor = 1.5
            height, width = gray.shape
            new_height, new_width = int(height * scale_factor), int(width * scale_factor)
            scaled = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)

            # 轻微锐化（增强字符边缘）
            kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
            sharpened = cv2.filter2D(scaled, -1, kernel * 0.5) # 减弱锐化强度

            # 限制在合理范围内
            result = np.clip(sharpened, 0, 255).astype(np.uint8)

            if self.debug_mode:
                self.logger.debug("百分比预处理v2完成")

            return result

        except Exception as e:
            self.logger.error(f"百分比预处理v2失败: {str(e)}")
            return image

    def preprocess_for_percentage_v4_conservative(self, image: np.ndarray) -> np.ndarray:
        """
        百分比预处理方法 v4 - 超保守策略
        专门针对负号和小数点保护的预处理

        Args:
            image: 原始图像

        Returns:
            处理后的图像
        """
        try:
            if self.debug_mode:
                self.logger.debug("开始百分比预处理v4（超保守策略）...")

            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # 非常轻微的缩放（避免字符变形）
            scale_factor = 2.0
            height, width = gray.shape
            new_height, new_width = int(height * scale_factor), int(width * scale_factor)
            scaled = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_CUBIC)

            # 非常轻微的对比度增强
            enhanced = cv2.convertScaleAbs(scaled, alpha=1.05, beta=2)

            # 使用非常小的高斯核去噪（保护细节）
            denoised = cv2.GaussianBlur(enhanced, (1, 1), 0.3)

            if self.debug_mode:
                self.logger.debug("百分比预处理v4完成")

            return denoised

        except Exception as e:
            self.logger.error(f"百分比预处理v4失败: {str(e)}")
            return image
    
    def preprocess_for_percentage_v3(self, image: np.ndarray) -> np.ndarray:
        """
        百分比预处理方法 v3 - 激进策略
        针对低质量图像的强化处理
        
        Args:
            image: 原始图像
            
        Returns:
            处理后的图像
        """
        try:
            if self.debug_mode:
                self.logger.debug("开始百分比预处理v3（激进策略）...")
            
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 大幅缩放
            scale_factor = 3.0
            height, width = gray.shape
            new_height, new_width = int(height * scale_factor), int(width * scale_factor)
            scaled = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
            
            # 强化对比度
            enhanced = cv2.convertScaleAbs(scaled, alpha=1.3, beta=15)
            
            # 高斯模糊去噪
            blurred = cv2.GaussianBlur(enhanced, (3, 3), 0)
            
            # 自适应阈值
            binary = cv2.adaptiveThreshold(
                blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                cv2.THRESH_BINARY, 9, 3
            )
            
            # 形态学闭操作（连接字符断裂部分）
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (2, 2))
            closed = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
            
            if self.debug_mode:
                self.logger.debug("百分比预处理v3完成")
            
            return closed
            
        except Exception as e:
            self.logger.error(f"百分比预处理v3失败: {str(e)}")
            return image
    
    def preprocess_for_percentage_multi_strategy(self, image: np.ndarray) -> List[Tuple[str, np.ndarray]]:
        """
        多策略百分比预处理
        生成多个不同预处理策略的结果
        
        Args:
            image: 原始图像
            
        Returns:
            多个预处理结果的列表，每个元素为 (策略名称, 处理后图像)
        """
        strategies = []
        
        try:
            # 策略1：标准预处理
            result1 = self.preprocess_for_percentage(image)
            strategies.append(('standard', result1))
            
            # 策略2：保守预处理
            result2 = self.preprocess_for_percentage_v2(image)
            strategies.append(('conservative', result2))
            
            # 策略3：激进预处理
            result3 = self.preprocess_for_percentage_v3(image)
            strategies.append(('aggressive', result3))
            
            # 策略4：无预处理（只放大）
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            scaled_only = cv2.resize(gray, None, fx=2.0, fy=2.0, interpolation=cv2.INTER_CUBIC)
            strategies.append(('scale_only', scaled_only))
            
            if self.debug_mode:
                self.logger.debug(f"生成了{len(strategies)}种预处理策略")
            
            return strategies
            
        except Exception as e:
            self.logger.error(f"多策略预处理失败: {str(e)}")
            # 返回原图作为后备
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            return [('fallback', gray)]
    
    def preprocess_image_simple(self, image: np.ndarray) -> np.ndarray:
        """
        简化的图像预处理（用于测试）
        
        Args:
            image: 原始图像
            
        Returns:
            简单处理后的图像
        """
        try:
            if self.debug_mode:
                self.logger.debug("使用简化预处理...")
            
            # 只进行灰度转换
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 轻微增强对比度
            enhanced = cv2.convertScaleAbs(gray, alpha=1.2, beta=10)
            
            if self.debug_mode:
                self.logger.debug("简化预处理完成")
            
            return enhanced
            
        except Exception as e:
            self.logger.error(f"简化预处理失败: {str(e)}")
            return image
    
    def preprocess_image_cpu_optimized(self, image: np.ndarray) -> np.ndarray:
        """
        针对CPU模式优化的图像预处理
        
        Args:
            image: 原始图像
            
        Returns:
            优化处理后的图像
        """
        try:
            if self.debug_mode:
                self.logger.debug("使用CPU优化预处理...")
            
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 适中的对比度增强（不要过度）
            enhanced = cv2.convertScaleAbs(gray, alpha=1.3, beta=20)
            
            # 轻微的高斯模糊去噪（核心更小）
            denoised = cv2.GaussianBlur(enhanced, (1, 1), 0)
            
            if self.debug_mode:
                self.logger.debug("CPU优化预处理完成")
            
            return denoised
            
        except Exception as e:
            self.logger.error(f"CPU优化预处理失败: {str(e)}")
            return image
    
    def preprocess_for_dark_text(self, image: np.ndarray) -> np.ndarray:
        """
        针对深色文本的预处理方法
        
        Args:
            image: 原始图像
            
        Returns:
            处理后的图像
        """
        try:
            if self.debug_mode:
                self.logger.debug("开始深色文本预处理...")
            
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 增强对比度（让深色文本更突出）
            enhanced = cv2.convertScaleAbs(gray, alpha=1.4, beta=10)
            
            # 轻微模糊去噪
            denoised = cv2.GaussianBlur(enhanced, (1, 1), 0)
            
            if self.debug_mode:
                self.logger.debug("深色文本预处理完成")
            
            return denoised
            
        except Exception as e:
            self.logger.error(f"深色文本预处理失败: {str(e)}")
            return image
    
    def preprocess_for_light_text(self, image: np.ndarray) -> np.ndarray:
        """
        针对浅色文本的预处理方法
        
        Args:
            image: 原始图像
            
        Returns:
            处理后的图像
        """
        try:
            if self.debug_mode:
                self.logger.debug("开始浅色文本预处理...")
            
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 反转颜色（浅色文本变深色）
            inverted = cv2.bitwise_not(gray)
            
            # 增强对比度
            enhanced = cv2.convertScaleAbs(inverted, alpha=1.2, beta=0)
            
            if self.debug_mode:
                self.logger.debug("浅色文本预处理完成")
            
            return enhanced
            
        except Exception as e:
            self.logger.error(f"浅色文本预处理失败: {str(e)}")
            return image
    
    def preprocess_simple(self, image: np.ndarray) -> np.ndarray:
        """
        简单预处理方法（最小化处理）
        
        Args:
            image: 原始图像
            
        Returns:
            简单处理后的图像
        """
        try:
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 高斯模糊
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)
            
            return blurred
            
        except Exception as e:
            self.logger.error(f"简单预处理失败: {str(e)}")
            return image
    
    def preprocess_scale_only(self, image: np.ndarray, scale_factor: float = 2.0) -> np.ndarray:
        """
        仅放大图像的预处理（无其他处理）
        
        Args:
            image: 原始图像
            scale_factor: 放大倍数
            
        Returns:
            放大后的图像
        """
        try:
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 放大图像
            height, width = gray.shape
            new_height, new_width = int(height * scale_factor), int(width * scale_factor)
            scaled = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
            
            return scaled
            
        except Exception as e:
            self.logger.error(f"仅放大预处理失败: {str(e)}")
            return image
    
    def save_debug_image(self, image: np.ndarray, filename: str):
        """
        保存调试图像
        
        Args:
            image: 图像数组
            filename: 文件名
        """
        if not self.save_debug_images:
            return
            
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_%f')[:-3]
            full_filename = f"{filename}_{timestamp}.png"
            file_path = os.path.join(self.debug_dir, full_filename)
            cv2.imwrite(file_path, image)
            if self.debug_mode:
                self.logger.debug(f"调试图像已保存: {file_path}")
        except Exception as e:
            self.logger.error(f"保存调试图像失败: {str(e)}")
    
    def visualize_capture_region(self, x: int, y: int, width: int, height: int) -> bool:
        """
        可视化截取区域，在截图上标记指定区域
        
        Args:
            x, y: 区域左上角坐标
            width, height: 区域尺寸
            
        Returns:
            是否成功保存了可视化图像
        """
        try:
            if self.debug_mode:
                self.logger.debug(f"开始可视化截取区域: ({x}, {y}, {width}, {height})")
            
            # 截取更大的区域用于显示上下文
            context_padding = 50
            context_x = max(0, x - context_padding)
            context_y = max(0, y - context_padding)
            context_width = width + 2 * context_padding
            context_height = height + 2 * context_padding
            
            # 截取上下文区域
            context_image = self.capture_region(context_x, context_y, context_width, context_height)
            if context_image is None:
                if self.debug_mode:
                    self.logger.debug("截取上下文区域失败")
                return False
            
            # 在图像上绘制目标区域框
            visualized = context_image.copy()
            
            # 计算目标区域在上下文图像中的相对位置
            rel_x = x - context_x
            rel_y = y - context_y
            
            # 绘制红色边框标记目标区域
            cv2.rectangle(visualized, 
                         (rel_x, rel_y), 
                         (rel_x + width, rel_y + height), 
                         (0, 0, 255),  # 红色BGR
                         2)  # 线条粗细
            
            # 添加标签文本
            label = f"Target: {width}x{height}"
            cv2.putText(visualized, label, 
                       (rel_x, rel_y - 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 
                       0.6, (0, 0, 255), 2)
            
            # 保存可视化图像
            if self.save_debug_images:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_%f')[:-3]
                filename = f"region_visualization_{x}_{y}_{width}_{height}_{timestamp}.png"
                filepath = os.path.join(self.debug_dir, filename)
                cv2.imwrite(filepath, visualized)
                
                if self.debug_mode:
                    self.logger.debug(f"区域可视化图像已保存: {filepath}")
                
                print(f"区域可视化图像已保存: {filepath}")
                print(f"红色框标记的是目标截取区域 ({x}, {y}, {width}, {height})")
                
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"可视化截取区域失败: {str(e)}")
            if self.debug_mode:
                self.logger.debug(f"可视化错误详情: {traceback.format_exc()}")
            return False
    
    def set_debug_dir(self, debug_dir: str):
        """
        设置调试图像保存目录

        Args:
            debug_dir: 调试目录路径
        """
        self.debug_dir = debug_dir
        if self.save_debug_images:
            os.makedirs(self.debug_dir, exist_ok=True)

    def create_test_image(self, text: str = "123.45万") -> np.ndarray:
        """
        创建测试图像

        Args:
            text: 要绘制的文本

        Returns:
            测试图像数组
        """
        try:
            # 创建白色背景图像
            test_img = np.ones((100, 300, 3), dtype=np.uint8) * 255

            # 添加文本（使用OpenCV绘制）
            font = cv2.FONT_HERSHEY_SIMPLEX
            cv2.putText(test_img, text, (50, 50), font, 1, (0, 0, 0), 2)

            if self.debug_mode:
                self.logger.debug(f"创建测试图像，文本: {text}")

            return test_img

        except Exception as e:
            self.logger.error(f"创建测试图像失败: {str(e)}")
            # 返回空白图像作为后备
            return np.ones((100, 300, 3), dtype=np.uint8) * 255
    
    def preprocess_for_percentage_enhanced(self, image: np.ndarray) -> np.ndarray:
        """
        百分数专用增强预处理方法
        特别针对小数点和百分号的识别优化
        
        Args:
            image: 原始图像
            
        Returns:
            处理后的图像
        """
        try:
            if self.debug_mode:
                self.logger.debug("开始百分数专用增强预处理...")
            
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 高倍放大以增加字符细节
            scale_factor = 4.0
            height, width = gray.shape
            new_height, new_width = int(height * scale_factor), int(width * scale_factor)
            scaled = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
            
            # 增强对比度，特别处理小字符
            enhanced = cv2.convertScaleAbs(scaled, alpha=1.4, beta=20)
            
            # 轻微高斯模糊减少噪声但保持字符边缘
            blurred = cv2.GaussianBlur(enhanced, (3, 3), 0.8)
            
            # 自适应阈值，适合不同光照条件
            binary = cv2.adaptiveThreshold(
                blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                cv2.THRESH_BINARY, 11, 2
            )
            
            # 使用小核心的形态学操作，避免字符粘连
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (1, 1))
            cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
            
            # 最后的锐化处理，增强字符边缘
            kernel_sharpen = np.array([[-1,-1,-1],
                                     [-1, 9,-1], 
                                     [-1,-1,-1]])
            sharpened = cv2.filter2D(cleaned, -1, kernel_sharpen)
            
            if self.debug_mode:
                self.logger.debug("百分数专用增强预处理完成")
            
            return sharpened
            
        except Exception as e:
            self.logger.error(f"百分数专用增强预处理失败: {str(e)}")
            return image
    
    def preprocess_for_micro_region(self, image: np.ndarray) -> np.ndarray:
        """
        超小区域专用预处理方法
        针对61x36这样的微小区域优化
        
        Args:
            image: 原始图像
            
        Returns:
            处理后的图像
        """
        try:
            if self.debug_mode:
                self.logger.debug("开始超小区域专用预处理...")
            
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 超高倍放大（针对61x36小区域）
            scale_factor = 6.0  # 从36高度放大到216像素
            height, width = gray.shape
            new_height, new_width = int(height * scale_factor), int(width * scale_factor)
            scaled = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)
            
            # 先进行轻微的双边滤波去噪但保护边缘
            filtered = cv2.bilateralFilter(scaled, 7, 50, 50)
            
            # 增强对比度（针对小字符）
            enhanced = cv2.convertScaleAbs(filtered, alpha=1.4, beta=15)
            
            # 轻微锐化以突出字符边缘
            kernel_sharpen = np.array([[-0.5,-0.5,-0.5],
                                     [-0.5, 5,-0.5], 
                                     [-0.5,-0.5,-0.5]])
            sharpened = cv2.filter2D(enhanced, -1, kernel_sharpen)
            
            # 限制在合理范围内
            result = np.clip(sharpened, 0, 255).astype(np.uint8)
            
            if self.debug_mode:
                self.logger.debug("超小区域专用预处理完成")
            
            return result
            
        except Exception as e:
            self.logger.error(f"超小区域专用预处理失败: {str(e)}")
            return image
    
    def preprocess_for_percentage_ultra(self, image: np.ndarray) -> np.ndarray:
        """
        百分数超强处理方法
        应对严重模糊或低质量的百分数图像
        
        Args:
            image: 原始图像
            
        Returns:
            处理后的图像
        """
        try:
            if self.debug_mode:
                self.logger.debug("开始百分数超强预处理...")
            
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 超高倍放大
            scale_factor = 5.0
            height, width = gray.shape
            new_height, new_width = int(height * scale_factor), int(width * scale_factor)
            scaled = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)
            
            # 直方图均衡化提高对比度
            equalized = cv2.equalizeHist(scaled)
            
            # 强化对比度
            enhanced = cv2.convertScaleAbs(equalized, alpha=1.6, beta=25)
            
            # 双边滤波：保持边缘的同时减少噪声
            filtered = cv2.bilateralFilter(enhanced, 9, 75, 75)
            
            # 自适应阈值，使用更大的邻域
            binary = cv2.adaptiveThreshold(
                filtered, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                cv2.THRESH_BINARY, 15, 4
            )
            
            # 开运算去除小噪点
            kernel_open = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (1, 1))
            opened = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel_open)
            
            # 闭运算连接字符断裂
            kernel_close = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (2, 2))
            closed = cv2.morphologyEx(opened, cv2.MORPH_CLOSE, kernel_close)
            
            if self.debug_mode:
                self.logger.debug("百分数超强预处理完成")
            
            return closed
            
        except Exception as e:
            self.logger.error(f"百分数超强预处理失败: {str(e)}")
            return image