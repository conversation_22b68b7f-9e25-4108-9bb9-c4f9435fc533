# -*- coding: utf-8 -*-
"""
指南针自动化模块
负责与指南针桌面软件的交互，获取股票多空资金数据
"""

import time
import logging
import subprocess
import os
from typing import Dict, Any, Optional, Tuple
import pywinauto
from pywinauto import Application, findwindows
import psutil

from config import COMPASS_SOFTWARE, APP_CONFIG
from compass_data_extractor import CompassDataExtractor
from smart_waiter import get_smart_waiter

class CompassAutomator:
    """指南针自动化控制类"""
    
    def __init__(self):
        """初始化自动化控制器"""
        self.logger = logging.getLogger(__name__)
        self.app = None
        self.main_window = None
        self.is_connected = False
        
        # 初始化数据提取器
        self.data_extractor = CompassDataExtractor()
        
        # 初始化智能等待器
        self.smart_waiter = get_smart_waiter()
    
    def start_compass_software(self) -> bool:
        """
        连接指南针软件（键盘操作模式）
        
        Returns:
            是否连接成功
        """
        try:
            # 在键盘操作模式下，只需要找到指南针窗口即可
            self.logger.info("尝试连接到指南针软件（键盘操作模式）")
            
            # 检查软件是否已经运行
            if self._is_compass_running():
                self.logger.info("指南针软件已在运行")
                return self._connect_to_compass_simple()
            
            # 如果软件未运行，提示用户手动启动
            self.logger.warning("指南针软件未运行，请手动启动指南针软件后重试")
            self.logger.info("提示：本工具支持键盘操作模式，无需复杂的软件路径配置")
            return False
            
        except Exception as e:
            self.logger.error(f"连接指南针软件失败: {str(e)}")
            return False
    
    def _is_compass_running(self) -> bool:
        """检查指南针软件是否正在运行"""
        try:
            process_name = COMPASS_SOFTWARE['process_name']
            for process in psutil.process_iter(['name']):
                if process.info['name'] == process_name:
                    return True
            return False
        except Exception:
            return False
    
    def _connect_to_compass_simple(self) -> bool:
        """简化的连接方法（键盘操作模式）"""
        try:
            # 尝试多种可能的窗口标题
            possible_titles = [
                COMPASS_SOFTWARE['main_window_title'],
                "指南针",
                "Compass",
                "全赢",
                "数据分析"
            ]
            
            for title in possible_titles:
                try:
                    # 尝试连接到应用程序
                    self.app = Application(backend="uia").connect(title_re=f".*{title}.*")
                    self.main_window = self.app.window(title_re=f".*{title}.*")
                    
                    if self.main_window.exists():
                        self.is_connected = True
                        # 设置数据提取器的主窗口
                        self.data_extractor.set_main_window(self.main_window)
                        self.logger.info(f"成功连接到指南针软件，窗口标题包含: {title}")
                        return True
                        
                except Exception:
                    continue
            
            # 如果以上都失败，尝试更宽松的匹配
            try:
                windows = findwindows.find_windows()
                for handle in windows:
                    try:
                        app = Application(backend="uia").connect(handle=handle)
                        window = app.window(handle=handle)
                        title = window.window_text()
                        
                        if any(keyword in title for keyword in ["指南针", "compass", "全赢", "数据", "股票"]):
                            self.app = app
                            self.main_window = window
                            self.is_connected = True
                            # 设置数据提取器的主窗口
                            self.data_extractor.set_main_window(self.main_window)
                            self.logger.info(f"成功连接到指南针软件，窗口标题: {title}")
                            return True
                    except Exception:
                        continue
                        
            except Exception:
                pass
            
            self.logger.error("未找到指南针软件窗口，请确保软件已运行")
            return False
            
        except Exception as e:
            self.logger.error(f"连接指南针软件失败: {str(e)}")
            return False

    def _connect_to_compass(self) -> bool:
        """连接到指南针软件"""
        try:
            # 尝试连接到应用程序
            self.app = Application(backend="uia").connect(
                title_re=f".*{COMPASS_SOFTWARE['main_window_title']}.*"
            )
            
            # 获取主窗口
            self.main_window = self.app.window(
                title_re=f".*{COMPASS_SOFTWARE['main_window_title']}.*"
            )
            
            if self.main_window.exists():
                self.is_connected = True
                # 设置数据提取器的主窗口
                self.data_extractor.set_main_window(self.main_window)
                self.logger.info("成功连接到指南针软件主窗口")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"连接指南针软件失败: {str(e)}")
            return False
    
    def search_stock(self, stock_code: str) -> bool:
        """
        搜索股票（智能等待优化版）
        
        Args:
            stock_code: 股票代码
            
        Returns:
            是否搜索成功
        """
        try:
            if not self.is_connected:
                self.logger.error("未连接到指南针软件")
                return False
            
            # 直接输入股票代码并回车（最简单的方式）
            self.main_window.type_keys(f"{stock_code}{{ENTER}}")  # 输入股票代码并回车确认
            
            # 使用智能等待替代固定等待
            actual_wait = self.smart_waiter.smart_page_load_wait(
                self.main_window, 
                f"股票{stock_code}页面加载"
            )
            
            self.logger.info(f"已搜索股票: {stock_code}")
            self.logger.debug(f"页面加载等待时间: {actual_wait:.2f}s")
            return True
            
        except Exception as e:
            self.logger.error(f"搜索股票 {stock_code} 失败: {str(e)}")
            return False
    
    def get_fund_flow_data(self, stock_code: str) -> Dict[str, float]:
        """
        获取股票的多空资金数据
        
        Args:
            stock_code: 股票代码
            
        Returns:
            包含三天资金数据的字典
        """
        try:
            if not self.is_connected:
                self.logger.error("未连接到指南针软件")
                return {}
            
            # 使用数据提取器获取资金数据
            return self.data_extractor.get_fund_flow_data(stock_code)
                
        except Exception as e:
            self.logger.error(f"获取股票 {stock_code} 资金数据失败: {str(e)}")
            return {}
    
    def analyze_single_stock(self, stock_code: str) -> Dict[str, Any]:
        """
        分析单只股票
        
        Args:
            stock_code: 股票代码
            
        Returns:
            分析结果字典
        """
        result = {
            'stock_code': stock_code,
            'status': '分析失败',
            'today_fund': 0.0,
            'yesterday_fund': 0.0,
            'day_before_yesterday_fund': 0.0
        }
        
        try:
            self.logger.info(f"开始分析股票: {stock_code}")
            
            # 搜索股票
            if not self.search_stock(stock_code):
                result['status'] = '搜索失败'
                self.logger.warning(f"股票 {stock_code} 搜索失败")
                return result
            
            # 获取资金流数据
            fund_data = self.get_fund_flow_data(stock_code)
            if fund_data:
                result.update(fund_data)  # 更新结果中的资金数据
                result['status'] = '分析成功'
                self.logger.info(f"股票 {stock_code} 分析成功，获取到 {len(fund_data)} 项资金数据")
            else:
                result['status'] = '获取资金数据失败'
                self.logger.warning(f"股票 {stock_code} 获取资金数据失败")
            
            return result
            
        except Exception as e:
            self.logger.error(f"分析股票 {stock_code} 时出错: {str(e)}")
            result['status'] = f'分析错误: {str(e)}'
            return result
    
    def close_compass_software(self):
        """关闭指南针软件连接"""
        try:
            # 显示智能等待器的统计信息
            self._log_wait_statistics()
            
            if self.app:
                self.app = None
            if self.main_window:
                self.main_window = None
            self.is_connected = False
            self.logger.info("已断开与指南针软件的连接")
        except Exception as e:
            self.logger.error(f"关闭指南针软件连接失败: {str(e)}")
    
    def _log_wait_statistics(self):
        """记录等待时间统计信息"""
        try:
            stats = self.smart_waiter.get_statistics()
            if stats['total_waits'] > 0:
                self.logger.info(f"智能等待统计：总等待{stats['total_waits']}次，"
                               f"平均{stats['average_wait']:.2f}s，"
                               f"节省时间{stats['total_saved_time']:.2f}s")
        except Exception as e:
            self.logger.debug(f"获取等待统计信息失败: {str(e)}")
    
    def __del__(self):
        """析构函数"""
        self.close_compass_software()