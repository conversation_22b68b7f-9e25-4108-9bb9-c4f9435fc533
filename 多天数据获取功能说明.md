# 多天资金数据获取功能实现文档

## 功能概述

已成功实现通过LEFT键导航获取三天（今天、昨天、前天）多空资金数据的功能。

## 主要新增功能

### 1. 键盘导航功能
- `send_left_key()`: 发送LEFT键切换到前一天
- `send_right_key()`: 发送RIGHT键切换到后一天  
- `wait_for_page_update()`: 等待页面数据更新完成

### 2. 单日数据获取
- `_get_current_day_fund_by_ocr()`: 通过OCR获取当前显示日期的多空资金数据

### 3. 多天数据获取核心功能
- `extract_multi_day_fund_data()`: 通过导航获取三天的多空资金数据

## 实现逻辑

1. **获取当天数据**: 在当前位置直接OCR识别
2. **获取昨天数据**: 发送LEFT键 → 等待页面更新 → OCR识别
3. **获取前天数据**: 再发送LEFT键 → 等待页面更新 → OCR识别  
4. **恢复到当天**: 发送两次RIGHT键返回原始位置

## 配置选项

在`config.py`中新增：
- `enable_multi_day_data`: 启用多天数据获取功能（默认True）

## 数据结构

返回包含三天数据的字典：
```python
{
    'today_fund': float,           # 今日多空资金
    'yesterday_fund': float,       # 昨日多空资金  
    'day_before_yesterday_fund': float  # 前日多空资金
}
```

## 错误处理

- OCR识别失败时返回0.0，继续获取其他天数据
- 键盘导航失败时记录错误并尝试恢复
- 异常发生时自动尝试恢复到当天位置

## 界面展示

GUI表格已支持显示三天数据：
- 序号 | 股票代码 | 今日资金 | 昨日资金 | 前日资金 | 状态

## 向后兼容性

- 保留原有的单页面OCR获取功能
- 通过配置开关控制是否启用多天数据获取
- 不影响现有的数据处理和显示逻辑

## 使用方式

功能已集成到现有流程中，用户无需额外操作：
1. 选择包含股票代码的Excel文件
2. 点击"开始分析"按钮  
3. 系统自动获取每只股票的三天资金数据
4. 结果显示在表格中，支持导出到Excel

## 技术特点

- 复用现有OCR系统，性能稳定
- 利用智能等待器确保数据准确性
- 完善的日志记录便于调试
- 自动恢复机制保证软件状态一致性