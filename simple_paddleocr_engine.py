# -*- coding: utf-8 -*-
"""
简化的PaddleOCR引擎实现
基于paddleocr_debug.py的成功经验，直接使用PaddleOCR处理原始图片
避免复杂的预处理策略，提高识别准确率
"""

import cv2
import numpy as np
import logging
import time
import traceback
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import re


class SimplePaddleOCREngine:
    """简化的PaddleOCR引擎"""
    
    def __init__(self, use_gpu: bool = False, debug_mode: bool = False):
        """
        初始化简化的PaddleOCR引擎
        
        Args:
            use_gpu: 是否使用GPU
            debug_mode: 是否启用调试模式
        """
        self.logger = logging.getLogger(__name__)
        self.debug_mode = debug_mode
        self.use_gpu = use_gpu
        self.paddle_reader = None
        self.is_initialized = False
        
        # 初始化PaddleOCR
        self._init_paddleocr()
    
    def _init_paddleocr(self):
        """初始化PaddleOCR"""
        try:
            self.logger.info(f"正在初始化PaddleOCR引擎 ({'GPU' if self.use_gpu else 'CPU'}模式)...")
            
            import paddleocr
            
            # 使用最简单的初始化参数，完全参考paddleocr_debug.py的成功配置
            self.paddle_reader = paddleocr.PaddleOCR(
                use_textline_orientation=True,  # 替代过时的use_angle_cls
                lang='ch'
            )
            
            self.is_initialized = True
            self.logger.info(f"PaddleOCR引擎初始化成功 ({'GPU' if self.use_gpu else 'CPU'}模式)")
            
        except Exception as e:
            self.logger.error(f"PaddleOCR引擎初始化失败: {str(e)}")
            if self.debug_mode:
                self.logger.debug(f"详细错误: {traceback.format_exc()}")
            
            # 如果GPU模式失败，尝试CPU模式
            if self.use_gpu:
                try:
                    self.logger.info("GPU模式失败，尝试CPU模式...")
                    import paddleocr
                    
                    self.paddle_reader = paddleocr.PaddleOCR(
                        use_textline_orientation=True,
                        lang='ch',
                        use_gpu=False,
                        show_log=self.debug_mode
                    )
                    
                    self.is_initialized = True
                    self.use_gpu = False
                    self.logger.info("PaddleOCR引擎初始化成功 (CPU回退模式)")
                    
                except Exception as e2:
                    self.logger.error(f"CPU模式初始化也失败: {str(e2)}")
                    self.is_initialized = False
    
    def is_available(self) -> bool:
        """检查PaddleOCR是否可用"""
        return self.is_initialized and self.paddle_reader is not None
    
    def recognize_image(self, image: np.ndarray) -> List[str]:
        """
        识别图像中的文本（直接处理原始图片）
        
        Args:
            image: 输入图像
            
        Returns:
            识别到的文本列表
        """
        if not self.is_available():
            self.logger.error("PaddleOCR引擎未初始化")
            return []
        
        try:
            if self.debug_mode:
                self.logger.debug(f"开始PaddleOCR识别，图像尺寸: {image.shape}")
            
            # 直接使用PaddleOCR识别原始图像，不进行预处理
            # 使用新的predict方法替代已弃用的ocr方法
            if hasattr(self.paddle_reader, 'predict'):
                results = self.paddle_reader.predict(image)
            else:
                # 回退到旧方法以保持兼容性
                results = self.paddle_reader.ocr(image)
            
            if self.debug_mode:
                self.logger.debug(f"PaddleOCR原始结果: {results}")
            
            # 提取文本，参考paddleocr_debug.py的实现
            texts = self._extract_texts_from_results(results)
            
            if self.debug_mode:
                self.logger.debug(f"提取到的文本: {texts}")
            
            return texts
            
        except Exception as e:
            self.logger.error(f"PaddleOCR识别失败: {str(e)}")
            if self.debug_mode:
                self.logger.debug(f"详细错误: {traceback.format_exc()}")
            return []
    
    def _extract_texts_from_results(self, results) -> List[str]:
        """
        从PaddleOCR结果中提取文本
        参考paddleocr_debug.py的实现
        """
        texts = []
        
        if not results:
            return texts
        
        try:
            # 处理新的OCRResult格式
            for result in results:
                # 检查是否是OCRResult对象
                if hasattr(result, '__dict__') or isinstance(result, dict):
                    # 尝试从字典或对象中提取rec_texts
                    rec_texts = None
                    
                    if isinstance(result, dict):
                        rec_texts = result.get('rec_texts', [])
                    else:
                        # 对象属性访问
                        rec_texts = getattr(result, 'rec_texts', [])
                    
                    # 添加识别到的文本
                    if rec_texts:
                        for text in rec_texts:
                            if text and text.strip():
                                texts.append(text.strip())
                else:
                    # 处理传统格式：[[bbox, (text, confidence)], ...]
                    if isinstance(result, list):
                        for item in result:
                            if isinstance(item, list) and len(item) >= 2:
                                # 提取文本部分
                                text_info = item[1]
                                if isinstance(text_info, tuple) and len(text_info) >= 1:
                                    text = text_info[0]
                                    if text and text.strip():
                                        texts.append(text.strip())
                                elif isinstance(text_info, str):
                                    if text_info.strip():
                                        texts.append(text_info.strip())
                                        
        except Exception as e:
            self.logger.error(f"文本提取失败: {e}")
            if self.debug_mode:
                self.logger.debug(f"详细错误: {traceback.format_exc()}")
        
        return texts
    
    def recognize_fund_data(self, image: np.ndarray) -> List[float]:
        """
        识别资金数据（专门用于股票资金识别）
        
        Args:
            image: 输入图像
            
        Returns:
            识别到的资金数值列表
        """
        texts = self.recognize_image(image)
        fund_values = []
        
        # 解析资金数据的正则表达式
        fund_pattern = r'[-+]?\d+\.?\d*%?'
        
        for text in texts:
            # 查找所有可能的数值
            matches = re.findall(fund_pattern, text)
            for match in matches:
                try:
                    # 移除百分号并转换为浮点数
                    value_str = match.replace('%', '')
                    value = float(value_str)
                    fund_values.append(value)
                    
                    if self.debug_mode:
                        self.logger.debug(f"解析资金数据: '{match}' -> {value}")
                        
                except ValueError:
                    continue
        
        return fund_values
    
    def capture_and_recognize_region(self, x: int, y: int, width: int, height: int) -> Dict[str, Any]:
        """
        截取屏幕区域并进行OCR识别
        
        Args:
            x, y: 区域左上角坐标
            width, height: 区域宽高
            
        Returns:
            识别结果字典
        """
        try:
            # 截取屏幕区域
            screenshot = self._capture_screen_region(x, y, width, height)
            if screenshot is None:
                return {'success': False, 'error': '截图失败'}
            
            # 识别资金数据
            fund_values = self.recognize_fund_data(screenshot)
            
            return {
                'success': True,
                'fund_values': fund_values,
                'strategy': 'simple_paddleocr',
                'confidence': 1.0 if fund_values else 0.0,
                'total_strategies': 1,
                'strategies_tried': 1,
                'early_exit': True,
                'execution_time': 0.0
            }
            
        except Exception as e:
            self.logger.error(f"区域识别失败: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def _capture_screen_region(self, x: int, y: int, width: int, height: int) -> Optional[np.ndarray]:
        """
        截取屏幕区域

        Args:
            x, y: 区域左上角坐标
            width, height: 区域宽高

        Returns:
            截图图像或None
        """
        try:
            # 使用现有的图像处理器进行截图
            from image_processor import ImageProcessor

            image_processor = ImageProcessor(debug_mode=self.debug_mode)
            screenshot = image_processor.capture_region(x, y, width, height)

            if self.debug_mode and screenshot is not None:
                self.logger.debug(f"截取屏幕区域: ({x}, {y}, {width}, {height}), 图像尺寸: {screenshot.shape}")

            return screenshot

        except Exception as e:
            self.logger.error(f"截取屏幕区域失败: {str(e)}")
            return None
