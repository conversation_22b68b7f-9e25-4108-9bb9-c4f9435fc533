# 筛选结果显示优化

**任务时间**: 2025-01-27T10:30:00

## 问题描述

用户反馈"筛选结果"表格显示了所有股票（包括不符合条件的），希望只显示符合条件的股票。

## 当前问题分析

1. **实时显示逻辑**: `analysis_worker` 中每分析完一只股票就通过 `("result", result)` 消息立即添加到表格
2. **无筛选显示**: `add_result_to_table` 方法会显示所有状态的股票（符合条件、不符合条件、数据无效）
3. **用户体验**: 用户需要在大量股票中寻找符合条件的，不够直观

## 解决方案

**方案**: 延迟表格更新 - 分析过程中不更新表格，筛选完成后批量显示符合条件的股票

### 实施步骤

1. **修改 `analysis_worker` 方法**:
   - 移除 `self.message_queue.put(("result", result))` 实时表格更新
   - 改为在日志中显示处理进度
   - 筛选完成后发送 `("filtered_results", filtered_results)` 消息

2. **添加新消息处理**:
   - 在 `check_message_queue` 中添加 `"filtered_results"` 消息类型处理

3. **创建批量更新方法**:
   - 新增 `update_table_with_filtered_results` 方法
   - 清空当前表格，只添加符合条件的股票
   - 重新编号，统一绿色背景显示

## 代码修改位置

### main.py

1. **analysis_worker 方法** (行 ~600):
   ```python
   # 移除实时表格更新
   - self.message_queue.put(("result", result))
   # 改为日志显示
   + self.message_queue.put(("log", f"已处理 {stock_code}: {result.get('status', '未知状态')}"))
   
   # 筛选完成后批量更新表格
   + self.message_queue.put(("filtered_results", filtered_results))
   ```

2. **check_message_queue 方法** (行 ~680):
   ```python
   + elif message_type == "filtered_results":
   +     self.update_table_with_filtered_results(data)
   ```

3. **新增方法 update_table_with_filtered_results**:
   - 清空表格
   - 只显示符合条件的股票
   - 重新编号和设置样式

## 预期效果

1. **筛选结果表格**: 只显示状态为"符合条件"的股票，统一绿色背景
2. **用户体验**: 更加直观，无需在大量数据中筛选
3. **进度显示**: 保持进度条和日志显示，用户能看到分析进度
4. **功能完整**: 保存功能仍基于 `filtered_stocks`，不受影响

## 测试要点

- [ ] 表格只显示符合条件的股票
- [ ] 序号重新编号（1, 2, 3...）
- [ ] 统计信息正确显示
- [ ] 保存功能正常工作
- [ ] 进度显示和日志记录正常

## 状态

- [x] 修改 analysis_worker 方法
- [x] 添加 filtered_results 消息处理
- [x] 创建批量更新表格方法
- [ ] 功能测试验证 