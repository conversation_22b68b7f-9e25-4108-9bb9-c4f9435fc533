# -*- coding: utf-8 -*-
"""
GUI股票分析模块
包含股票分析的核心逻辑
"""

import os
import threading
import traceback
from tkinter import messagebox

from compass_automator import CompassAutomator


class GUIAnalysisMixin:
    """GUI股票分析Mixin类"""
    
    def start_analysis(self):
        """开始分析"""
        if not self.current_excel_path:
            messagebox.showerror("错误", "请先选择Excel文件")
            return
            
        if not os.path.exists(self.current_excel_path):
            messagebox.showerror("错误", "Excel文件不存在")
            return
            
        self.is_processing = True
        self.start_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        self.save_btn.config(state="disabled")
        
        # 清空结果表格
        for item in self.result_tree.get_children():
            self.result_tree.delete(item)
            
        # 启动分析线程
        analysis_thread = threading.Thread(target=self.analysis_worker, daemon=True)
        analysis_thread.start()
        
    def analysis_worker(self):
        """分析工作线程"""
        try:
            # 读取Excel文件
            self.message_queue.put(("status", "正在读取Excel文件..."))
            self.message_queue.put(("log", f"开始读取Excel文件: {self.current_excel_path}"))
            
            stock_codes = self.data_processor.load_excel_file(self.current_excel_path)
            total_stocks = len(stock_codes)
            
            self.message_queue.put(("log", f"读取到 {total_stocks} 个股票代码"))
            
            # 初始化指南针自动化
            self.message_queue.put(("status", "正在连接指南针软件..."))
            self.compass_automator = CompassAutomator()
            
            if not self.compass_automator.start_compass_software():
                self.message_queue.put(("log", "无法连接到指南针软件"))
                self.message_queue.put(("status", "连接指南针软件失败"))
                return
                
            # 分析每只股票
            all_results = []  # 存储所有分析结果
            for i, stock_code in enumerate(stock_codes):
                if not self.is_processing:
                    break
                    
                progress = (i + 1) / total_stocks * 100
                self.message_queue.put(("progress", progress))
                self.message_queue.put(("status", f"正在分析 {stock_code} ({i+1}/{total_stocks})"))
                
                # 分析股票
                result = self.compass_automator.analyze_single_stock(stock_code)
                all_results.append(result)
                
                # 在日志中显示处理进度，包含资金数据
                status = result.get('status', '未知状态')
                today_fund = result.get('today_fund', 0.0)
                yesterday_fund = result.get('yesterday_fund', 0.0)
                day_before_fund = result.get('day_before_yesterday_fund', 0.0)
                
                # 格式化资金数据显示
                fund_info = f"今日{today_fund:+.3f}%；昨日{yesterday_fund:+.3f}%；前日{day_before_fund:+.3f}%"
                self.message_queue.put(("log", f"已处理 {stock_code}：{status} {fund_info}"))
                
            # 存储所有结果以供保存使用
            self.all_analysis_results = all_results
            
            # 筛选结果
            self.message_queue.put(("status", "正在筛选结果..."))
            filtered_results = self.data_processor.filter_stocks(all_results)
            
            # 更新表格显示筛选结果
            self.message_queue.put(("filtered_results", filtered_results))
            
            # 显示统计信息
            stats = self.data_processor.get_statistics()
            success_count = len([r for r in all_results if r.get('status') == '分析成功'])
            failed_count = len(all_results) - success_count
            
            self.message_queue.put(("log", f"分析统计: 总计 {len(all_results)} 只股票"))
            self.message_queue.put(("log", f"  成功分析: {success_count} 只"))
            self.message_queue.put(("log", f"  分析失败: {failed_count} 只"))
            self.message_queue.put(("log", f"  符合筛选条件: {stats['filtered_stocks']} 只"))
            
            self.message_queue.put(("status", "分析完成"))
            self.message_queue.put(("enable_save", True))
            
        except Exception as e:
            self.message_queue.put(("log", f"分析过程中发生错误: {str(e)}"))
            self.message_queue.put(("log", traceback.format_exc()))
            self.message_queue.put(("status", "分析失败"))
        finally:
            self.is_processing = False
            self.message_queue.put(("analysis_complete", None))
            if self.compass_automator:
                self.compass_automator.close_compass_software()
                
    def stop_analysis(self):
        """停止分析"""
        self.is_processing = False
        self.message_queue.put(("status", "正在停止分析..."))
        self.message_queue.put(("log", "用户请求停止分析"))