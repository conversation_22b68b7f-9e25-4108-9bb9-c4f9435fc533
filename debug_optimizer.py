# -*- coding: utf-8 -*-
"""
调试输出优化模块
提供智能调试日志管理，减少性能影响
"""

import logging
import time
from typing import Dict, Any, Set
from functools import wraps
from collections import defaultdict, deque


class DebugOptimizer:
    """调试输出优化器"""
    
    def __init__(self, max_repetitions: int = 3, time_window: int = 60):
        """
        初始化调试优化器
        
        Args:
            max_repetitions: 在时间窗口内允许的最大重复消息数
            time_window: 时间窗口长度（秒）
        """
        self.logger = logging.getLogger(__name__)
        self.max_repetitions = max_repetitions
        self.time_window = time_window
        
        # 消息计数器和时间戳
        self.message_counts = defaultdict(int)
        self.message_timestamps = defaultdict(deque)
        self.suppressed_messages = set()
        
        # 性能监控
        self.start_time = time.time()
        self.total_messages = 0
        self.suppressed_count = 0
    
    def should_log_message(self, message: str, level: int) -> bool:
        """
        检查是否应该记录这条消息
        
        Args:
            message: 日志消息
            level: 日志级别
            
        Returns:
            是否应该记录
        """
        # 对于ERROR和WARNING级别，总是记录
        if level >= logging.WARNING:
            return True
        
        current_time = time.time()
        message_key = message
        
        # 清理过期的时间戳
        timestamps = self.message_timestamps[message_key]
        while timestamps and current_time - timestamps[0] > self.time_window:
            timestamps.popleft()
        
        # 检查是否超过重复限制
        if len(timestamps) >= self.max_repetitions:
            if message_key not in self.suppressed_messages:
                self.suppressed_messages.add(message_key)
                self.logger.info(f"调试消息抑制: '{message[:50]}...' (后续重复消息将在{self.time_window}秒内被抑制)")
            self.suppressed_count += 1
            return False
        
        # 记录时间戳并允许记录
        timestamps.append(current_time)
        self.message_counts[message_key] += 1
        self.total_messages += 1
        
        # 如果之前被抑制过，现在可以记录了
        if message_key in self.suppressed_messages:
            self.suppressed_messages.remove(message_key)
        
        return True
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取优化统计信息"""
        uptime = time.time() - self.start_time
        return {
            'uptime_seconds': uptime,
            'total_messages': self.total_messages,
            'suppressed_messages': self.suppressed_count,
            'suppression_rate': (self.suppressed_count / max(1, self.total_messages)) * 100,
            'unique_message_types': len(self.message_counts),
            'currently_suppressed': len(self.suppressed_messages)
        }
    
    def reset_statistics(self):
        """重置统计信息"""
        self.message_counts.clear()
        self.message_timestamps.clear()
        self.suppressed_messages.clear()
        self.start_time = time.time()
        self.total_messages = 0
        self.suppressed_count = 0


class OptimizedLogger:
    """优化的日志记录器"""
    
    def __init__(self, logger_name: str):
        """
        初始化优化日志记录器
        
        Args:
            logger_name: 日志记录器名称
        """
        self.logger = logging.getLogger(logger_name)
        self.optimizer = DebugOptimizer()
    
    def debug(self, message: str, *args, **kwargs):
        """记录DEBUG级别消息"""
        if self.optimizer.should_log_message(message, logging.DEBUG):
            self.logger.debug(message, *args, **kwargs)
    
    def info(self, message: str, *args, **kwargs):
        """记录INFO级别消息"""
        if self.optimizer.should_log_message(message, logging.INFO):
            self.logger.info(message, *args, **kwargs)
    
    def warning(self, message: str, *args, **kwargs):
        """记录WARNING级别消息（总是记录）"""
        self.logger.warning(message, *args, **kwargs)
    
    def error(self, message: str, *args, **kwargs):
        """记录ERROR级别消息（总是记录）"""
        self.logger.error(message, *args, **kwargs)
    
    def critical(self, message: str, *args, **kwargs):
        """记录CRITICAL级别消息（总是记录）"""
        self.logger.critical(message, *args, **kwargs)
    
    def get_optimization_stats(self) -> Dict[str, Any]:
        """获取优化统计信息"""
        return self.optimizer.get_statistics()


def optimized_debug_log(func):
    """优化调试日志的装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        # 临时降低日志级别以减少调试输出
        original_level = logging.getLogger().level
        if original_level <= logging.DEBUG:
            # 在性能敏感的操作中，临时提高日志级别
            logging.getLogger().setLevel(logging.INFO)
        
        try:
            result = func(*args, **kwargs)
            return result
        finally:
            # 恢复原始日志级别
            logging.getLogger().setLevel(original_level)
    
    return wrapper


# 全局调试优化器实例
global_debug_optimizer = DebugOptimizer()


def get_optimized_logger(name: str) -> OptimizedLogger:
    """
    获取优化的日志记录器实例
    
    Args:
        name: 日志记录器名称
        
    Returns:
        优化的日志记录器
    """
    return OptimizedLogger(name)


def get_debug_stats() -> Dict[str, Any]:
    """获取全局调试优化统计信息"""
    return global_debug_optimizer.get_statistics()