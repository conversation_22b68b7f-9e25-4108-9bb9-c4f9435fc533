# -*- coding: utf-8 -*-
"""
简化OCR管理器
专门管理简化PaddleOCR引擎，避免复杂依赖
"""

import threading
import logging
from typing import Dict, Any
from paddleocr_compatibility_wrapper import create_paddleocr_wrapper
from config import APP_CONFIG


class SimpleOCRManager:
    """简化OCR管理器单例类"""
    
    _instance = None
    _lock = threading.Lock()
    _paddleocr_wrapper = None
    _initialized = False
    
    def __new__(cls):
        """单例模式实现"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(SimpleOCRManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化简化OCR管理器"""
        if not self._initialized:
            self.logger = logging.getLogger(__name__)
            self._initialized = True
    
    def initialize_ocr(self, use_gpu: bool = None, debug_mode: bool = None) -> bool:
        """
        初始化简化PaddleOCR引擎
        
        Args:
            use_gpu: 是否使用GPU，默认从配置读取
            debug_mode: 是否开启调试模式，默认从配置读取
            
        Returns:
            初始化是否成功
        """
        if self._paddleocr_wrapper is not None:
            self.logger.info("简化PaddleOCR引擎已初始化，无需重复初始化")
            return True
            
        try:
            # 从配置获取默认值
            ocr_config = APP_CONFIG.get('ocr_settings', {})
            
            if use_gpu is None:
                use_gpu = ocr_config.get('use_gpu', False)
            if debug_mode is None:
                debug_mode = ocr_config.get('debug_mode', True)
            
            self.logger.info(f"正在初始化简化PaddleOCR引擎... (GPU: {use_gpu}, 调试: {debug_mode})")
            
            # 创建简化PaddleOCR包装器
            self._paddleocr_wrapper = create_paddleocr_wrapper(use_gpu=use_gpu, debug_mode=debug_mode)
            
            if self._paddleocr_wrapper.is_available():
                self.logger.info("简化PaddleOCR引擎初始化成功")
                status = self._paddleocr_wrapper.get_engine_status()
                self.logger.info(f"引擎状态: {status}")
                return True
            else:
                self.logger.error("简化PaddleOCR引擎初始化失败")
                self._paddleocr_wrapper = None
                return False
                
        except Exception as e:
            self.logger.error(f"简化PaddleOCR引擎初始化异常: {str(e)}")
            self._paddleocr_wrapper = None
            return False
    
    def get_ocr_engine(self):
        """
        获取简化PaddleOCR引擎实例
        
        Returns:
            简化PaddleOCR引擎实例或None
        """
        if self._paddleocr_wrapper is None:
            self.logger.warning("简化PaddleOCR引擎未初始化，尝试自动初始化...")
            if not self.initialize_ocr():
                self.logger.error("自动初始化简化PaddleOCR引擎失败")
                return None
        
        return self._paddleocr_wrapper
    
    def get_optimized_ocr_engine(self):
        """
        获取优化版OCR引擎实例（对于简化版，直接返回主引擎）
        
        Returns:
            简化PaddleOCR引擎实例
        """
        return self.get_ocr_engine()
    
    def is_initialized(self) -> bool:
        """
        检查OCR引擎是否已初始化
        
        Returns:
            是否已初始化
        """
        return self._paddleocr_wrapper is not None and self._paddleocr_wrapper.is_available()
    
    def get_engine_status(self) -> Dict[str, Any]:
        """
        获取OCR引擎状态
        
        Returns:
            引擎状态信息
        """
        if self._paddleocr_wrapper is None:
            return {
                'initialized': False,
                'available_engines': {},
                'error_count': 0,
                'engine_mode': 'simple_paddleocr'
            }
        
        try:
            status = self._paddleocr_wrapper.get_engine_status()
            status['initialized'] = True
            status['optimization_enabled'] = True  # 简化版本身就是优化的
            return status
        except Exception as e:
            self.logger.error(f"获取OCR引擎状态失败: {str(e)}")
            return {
                'initialized': False,
                'available_engines': {},
                'error_count': 999,
                'error': str(e),
                'engine_mode': 'simple_paddleocr'
            }
    
    def reset_error_count(self):
        """重置错误计数"""
        if self._paddleocr_wrapper:
            self._paddleocr_wrapper.reset_statistics()
            self.logger.debug("简化PaddleOCR错误计数已重置")
    
    def get_optimization_stats(self) -> Dict[str, Any]:
        """获取优化统计信息"""
        if self._paddleocr_wrapper:
            return self._paddleocr_wrapper.get_statistics()
        return {'optimization_available': False}
    
    def cleanup(self):
        """清理OCR引擎资源"""
        try:
            # 记录统计信息
            if self._paddleocr_wrapper:
                stats = self._paddleocr_wrapper.get_statistics()
                if stats.get('total_calls', 0) > 0:
                    self.logger.info(f"简化PaddleOCR统计：调用{stats['total_calls']}次，"
                                   f"成功{stats['successful_calls']}次，"
                                   f"成功率{stats['success_rate']:.1f}%")
            
            # 清理资源
            if self._paddleocr_wrapper:
                self.logger.info("清理简化PaddleOCR引擎资源")
                self._paddleocr_wrapper = None
                
        except Exception as e:
            self.logger.error(f"清理简化PaddleOCR引擎资源失败: {str(e)}")


# 全局单例实例
simple_ocr_manager = SimpleOCRManager()


def get_simple_ocr_manager() -> SimpleOCRManager:
    """
    获取简化OCR管理器实例
    
    Returns:
        简化OCR管理器单例
    """
    return simple_ocr_manager


def get_global_ocr_manager():
    """
    兼容性函数：获取OCR管理器
    根据配置返回简化或复杂OCR管理器
    
    Returns:
        OCR管理器实例
    """
    ocr_config = APP_CONFIG.get('ocr_settings', {})
    engine_mode = ocr_config.get('engine_mode', 'simple_paddleocr')
    
    if engine_mode == 'simple_paddleocr':
        return get_simple_ocr_manager()
    else:
        # 尝试导入复杂OCR管理器
        try:
            from ocr_manager_optimized import get_global_ocr_manager as get_complex_ocr_manager
            return get_complex_ocr_manager()
        except ImportError:
            # 如果复杂OCR管理器不可用，回退到简化版
            logging.getLogger(__name__).warning("复杂OCR管理器不可用，使用简化版")
            return get_simple_ocr_manager()
