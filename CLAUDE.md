# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.
Always respond in Chinese-simplified.

## Project Overview

This is a Chinese stock screening tool that automates data extraction from Compass (指南针) financial software using OCR and GUI automation. The application extracts fund flow data for multiple stocks and filters them based on configurable criteria.

## Key Architecture Components

### Core Modules
- **main.py**: Tkinter-based GUI application entry point with comprehensive stock analysis interface
- **config.py**: Central configuration with software paths, OCR settings, and filtering logic
- **compass_automator.py**: Windows automation for Compass software interaction using pywinauto
- **data_processor.py**: Excel file processing and stock filtering logic with pandas
- **compass_data_extractor.py**: Core data extraction logic combining keyboard automation and OCR

### OCR System (Multi-Engine)
- **ocr_engines.py**: Manages EasyOCR and PaddleOCR engine initialization
- **ocr_manager_optimized.py**: Global OCR manager with strategy optimization
- **ocr_strategy_optimizer.py**: Dynamic OCR strategy selection and optimization
- **unified_ocr_component.py**: Unified OCR interface with preprocessing strategies
- **ocr_performance_wrapper.py**: Performance monitoring and error handling

### Specialized Components
- **region_selector.py**: Interactive screen region selection for OCR
- **image_processor.py**: Image preprocessing and enhancement for OCR accuracy
- **smart_waiter.py**: Intelligent waiting strategies for GUI automation
- **ocr_diagnostics.py**: Comprehensive OCR system health checking

## Development Commands

### Installation
```bash
pip install -r requirements.txt
```

### Running the Application
```bash
python main.py
```

### Testing OCR Components
Individual test files are available (though excluded from git):
```bash
python test_ocr_fix.py                    # OCR functionality tests
python test_percentage_ocr_comprehensive.py  # Percentage recognition tests
python test_percentage_parser.py          # Parser validation tests
```

## Configuration Management

### Main Configuration (`config.py`)
- **COMPASS_SOFTWARE**: Compass software paths and window detection settings
- **APP_CONFIG**: Application behavior, OCR settings, and processing parameters
- **OCR_CONFIG**: Multi-engine OCR configuration with fallback strategies
- **GUI_CONFIG**: UI layout and appearance settings
- **FILTER_CONFIG**: Stock filtering criteria and validation rules

### Dynamic Configuration
- OCR region coordinates are automatically saved to `config.py` when selected via GUI
- The `update_ocr_region_config()` function handles real-time configuration updates

## Key Design Patterns

### OCR Strategy Pattern
The system employs multiple OCR strategies that are dynamically selected based on:
- Image quality assessment
- Historical performance data
- Content type (text vs percentages)

### Manager Pattern
- **OCRManager**: Global singleton managing multiple OCR engines
- **DataExtractor**: Orchestrates keyboard automation and OCR operations
- **SmartWaiter**: Adaptive timing for GUI operations

### Multi-Engine Fallback
OCR operations use a fallback hierarchy:
1. EasyOCR with GPU acceleration
2. PaddleOCR as backup
3. Different preprocessing strategies for each engine

## Important Development Notes

### Windows-Specific Dependencies
- This application is designed for Windows and requires Compass financial software
- Uses pywinauto for Windows GUI automation
- Screen capture functionality depends on Windows APIs

### OCR Requirements
- First-time EasyOCR initialization downloads large ML models (~100MB+)
- GPU acceleration requires CUDA-compatible hardware
- Debug images are automatically saved to assist with OCR troubleshooting

### Threading Architecture
- GUI operations run on main thread
- OCR processing and automation occur in background threads
- Message queue system handles thread communication

### Error Handling Strategy
- OCR operations have built-in retry mechanisms with strategy switching
- Comprehensive error logging and user notification systems
- Graceful degradation when OCR engines fail

## File Exclusions

The project uses a comprehensive .gitignore that excludes:
- All test files (`test_*.py`)
- OCR debug images and temporary screenshots
- User data files (Excel, logs, configurations)
- Development tools and IDE configurations

This keeps the repository focused on core business logic while enabling local development and debugging.

## Code Organization Guidelines

### File Size Management
如果文件超过500行，则按模块或者功能拆分成小文件。当进行代码重构时，应该：
- 将相关功能分组到独立的模块中
- 保持单一职责原则，每个文件专注于特定功能
- 使用清晰的模块命名约定
- 通过导入语句维护模块间的依赖关系