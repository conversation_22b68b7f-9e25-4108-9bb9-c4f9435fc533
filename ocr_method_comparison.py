# -*- coding: utf-8 -*-
"""
OCR方法对比测试脚本
测试不同的图像预处理方法和OCR引擎组合，找到最佳识别方案
输出Excel格式的详细对比结果
"""

import os
import time
import warnings
from pathlib import Path
from typing import Dict, List, Any, Tuple
import cv2
import numpy as np

# 抑制警告
warnings.filterwarnings("ignore")
os.environ['PYTORCH_DISABLE_PIN_MEMORY_WARNING'] = '1'

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    print("警告: pandas未安装，将无法生成Excel输出")
    print("请安装: pip install pandas openpyxl")

class OCRMethodComparison:
    """OCR方法对比测试器"""
    
    def __init__(self, use_gpu: bool = False, debug_mode: bool = False):
        """
        初始化对比测试器
        
        Args:
            use_gpu: 是否使用GPU加速
            debug_mode: 是否启用调试模式
        """
        self.use_gpu = use_gpu
        self.debug_mode = debug_mode
        self.easyocr_reader = None
        self.paddleocr_reader = None
        self.test_images_dir = Path("debug_images_20250724_221758")
        
        print(f"=== OCR方法对比测试器 ===")
        print(f"GPU模式: {'启用' if use_gpu else '禁用'}")
        print(f"调试模式: {'启用' if debug_mode else '禁用'}")
        
        # 初始化OCR引擎
        self._init_ocr_engines()
    
    def _init_ocr_engines(self):
        """初始化OCR引擎"""
        print("\n--- 初始化OCR引擎 ---")
        
        # 初始化EasyOCR
        try:
            print("正在初始化EasyOCR...")
            import easyocr
            self.easyocr_reader = easyocr.Reader(['ch_sim', 'en'], gpu=self.use_gpu, verbose=False)
            print("✓ EasyOCR初始化成功")
        except Exception as e:
            print(f"✗ EasyOCR初始化失败: {e}")
            
        # 初始化PaddleOCR
        try:
            print("正在初始化PaddleOCR...")
            import paddleocr
            self.paddleocr_reader = paddleocr.PaddleOCR(
                use_textline_orientation=True,  # 替代过时的use_angle_cls
                lang='ch'
            )
            print("✓ PaddleOCR初始化成功")
        except Exception as e:
            print(f"✗ PaddleOCR初始化失败: {e}")
    
    def _get_preprocessing_methods(self) -> List[Tuple[str, callable]]:
        """获取所有预处理方法"""
        methods = [
            ("原图直接识别", lambda img: img),
            ("基础预处理", self._preprocess_basic),
            ("放大2倍", lambda img: self._preprocess_scale(img, 2.0)),
            ("放大3倍", lambda img: self._preprocess_scale(img, 3.0)),
            ("放大4倍", lambda img: self._preprocess_scale(img, 4.0)),
            ("放大5倍", lambda img: self._preprocess_scale(img, 5.0)),
            ("放大6倍", lambda img: self._preprocess_scale(img, 6.0)),
            ("放大8倍", lambda img: self._preprocess_scale(img, 8.0)),
            ("放大10倍", lambda img: self._preprocess_scale(img, 10.0)),
            ("放大12倍", lambda img: self._preprocess_scale(img, 12.0)),
            ("灰度+二值化", self._preprocess_gray_binary),
            ("增强对比度", self._preprocess_enhance_contrast),
            ("去噪处理", self._preprocess_denoise),
            ("锐化处理", self._preprocess_sharpen),
            ("综合处理", self._preprocess_comprehensive),
        ]
        return methods
    
    def _preprocess_basic(self, image: np.ndarray) -> np.ndarray:
        """基础预处理"""
        # 转灰度
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        
        # 调整对比度
        enhanced = cv2.convertScaleAbs(gray, alpha=1.2, beta=10)
        return enhanced
    
    def _preprocess_scale(self, image: np.ndarray, scale_factor: float) -> np.ndarray:
        """仅放大处理"""
        height, width = image.shape[:2]
        new_width = int(width * scale_factor)
        new_height = int(height * scale_factor)
        return cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
    
    def _preprocess_gray_binary(self, image: np.ndarray) -> np.ndarray:
        """灰度+二值化处理"""
        # 转灰度
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        
        # 自适应二值化
        binary = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                     cv2.THRESH_BINARY, 11, 2)
        return binary
    
    def _preprocess_enhance_contrast(self, image: np.ndarray) -> np.ndarray:
        """增强对比度"""
        # 转灰度
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        
        # CLAHE对比度限制自适应直方图均衡化
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)
        return enhanced
    
    def _preprocess_denoise(self, image: np.ndarray) -> np.ndarray:
        """去噪处理"""
        # 转灰度
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        
        # 高斯滤波去噪
        denoised = cv2.GaussianBlur(gray, (3, 3), 0)
        return denoised
    
    def _preprocess_sharpen(self, image: np.ndarray) -> np.ndarray:
        """锐化处理"""
        # 转灰度
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        
        # 锐化核
        kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        sharpened = cv2.filter2D(gray, -1, kernel)
        return sharpened
    
    def _preprocess_comprehensive(self, image: np.ndarray) -> np.ndarray:
        """综合处理：放大+去噪+对比度增强"""
        # 放大3倍
        scaled = self._preprocess_scale(image, 3.0)
        
        # 转灰度
        if len(scaled.shape) == 3:
            gray = cv2.cvtColor(scaled, cv2.COLOR_BGR2GRAY)
        else:
            gray = scaled
        
        # 轻微去噪
        denoised = cv2.GaussianBlur(gray, (3, 3), 0)
        
        # 增强对比度
        enhanced = cv2.convertScaleAbs(denoised, alpha=1.5, beta=20)
        
        return enhanced
    
    def _recognize_with_easyocr(self, image: np.ndarray) -> List[str]:
        """使用EasyOCR识别"""
        if not self.easyocr_reader:
            return []
        
        try:
            results = self.easyocr_reader.readtext(image)
            texts = [result[1] for result in results if len(result) >= 2]
            return texts
        except Exception:
            return []
    
    def _recognize_with_paddleocr(self, image: np.ndarray) -> List[str]:
        """使用PaddleOCR识别"""
        if not self.paddleocr_reader:
            return []
        
        try:
            # 调用PaddleOCR进行识别（不使用cls参数）
            results = self.paddleocr_reader.ocr(image)
            
            # 提取文本从新的OCRResult格式
            texts = []
            if results:
                for result in results:
                    # 检查是否是OCRResult对象
                    if hasattr(result, '__dict__') or isinstance(result, dict):
                        # 尝试从字典或对象中提取rec_texts
                        rec_texts = None
                        
                        if isinstance(result, dict):
                            rec_texts = result.get('rec_texts', [])
                        else:
                            # 对象属性访问
                            rec_texts = getattr(result, 'rec_texts', [])
                        
                        # 添加识别到的文本
                        if rec_texts:
                            for text in rec_texts:
                                if text and text.strip():
                                    texts.append(text.strip())
                    
            return texts
            
        except Exception as e:
            # 调试信息：输出错误详情
            if self.debug_mode:
                print(f"PaddleOCR识别异常: {e}")
                import traceback
                print(f"详细错误: {traceback.format_exc()}")
            return []
    
    def _is_percentage_data(self, text: str) -> bool:
        """判断是否为百分数数据"""
        if not text:
            return False
        
        # 包含百分号
        if '%' in text:
            return True
        
        # 包含负号和数字
        if '-' in text and any(c.isdigit() for c in text):
            return True
        
        # 纯数字（可能是百分数）
        clean_text = text.replace('.', '').replace('-', '').replace('+', '')
        if clean_text.replace(',', '').isdigit():
            return True
        
        return False
    
    def _evaluate_recognition_quality(self, texts: List[str]) -> Dict[str, Any]:
        """评估识别质量"""
        if not texts:
            return {"score": 0.0, "percentage_count": 0, "total_count": 0}
        
        percentage_count = sum(1 for text in texts if self._is_percentage_data(text))
        total_count = len(texts)
        
        # 计算质量分数
        if total_count == 0:
            score = 0.0
        else:
            # 基础分数：基于识别到的文本数量
            base_score = min(total_count / 3.0, 1.0)  # 假设期望识别3个文本
            
            # 百分数识别加分
            percentage_bonus = percentage_count * 0.3
            
            # 综合分数
            score = min(base_score + percentage_bonus, 1.0)
        
        return {
            "score": score,
            "percentage_count": percentage_count,
            "total_count": total_count,
            "texts": texts
        }
    
    def _format_ocr_results(self, texts: List[str]) -> str:
        """格式化OCR识别结果为字符串"""
        if not texts:
            return "识别失败"
        
        # 如果有多个结果，用分号连接
        return "; ".join(texts) if len(texts) > 1 else texts[0]
    
    def test_single_image(self, image_path: Path) -> Dict[str, Any]:
        """测试单张图片的所有方法组合"""
        print(f"\n--- 测试图片: {image_path.name} ---")
        
        # 读取图片
        image = cv2.imread(str(image_path))
        if image is None:
            return {"error": "图片读取失败"}
        
        # 获取所有预处理方法
        preprocessing_methods = self._get_preprocessing_methods()
        
        # 为Excel准备数据结构
        easyocr_row = {"图片名称": image_path.name}
        paddleocr_row = {"图片名称": image_path.name}
        
        for method_name, preprocess_func in preprocessing_methods:
            try:
                # 预处理
                processed_image = preprocess_func(image)
                
                # EasyOCR识别
                easyocr_result = "识别失败"
                if self.easyocr_reader:
                    easyocr_texts = self._recognize_with_easyocr(processed_image)
                    easyocr_result = self._format_ocr_results(easyocr_texts)
                
                # PaddleOCR识别
                paddleocr_result = "识别失败"
                if self.paddleocr_reader:
                    paddleocr_texts = self._recognize_with_paddleocr(processed_image)
                    paddleocr_result = self._format_ocr_results(paddleocr_texts)
                
                # 存储结果
                easyocr_row[method_name] = easyocr_result
                paddleocr_row[method_name] = paddleocr_result
                
                # 简单进度显示
                easy_count = len(easyocr_texts) if self.easyocr_reader and easyocr_result != "识别失败" else 0
                paddle_count = len(paddleocr_texts) if self.paddleocr_reader and paddleocr_result != "识别失败" else 0
                print(f"  {method_name}: EasyOCR({easy_count}), PaddleOCR({paddle_count})")
                
            except Exception as e:
                print(f"  {method_name}: 处理失败 - {e}")
                easyocr_row[method_name] = "处理失败"
                paddleocr_row[method_name] = "处理失败"
                continue
        
        return {
            "success": True,
            "image_name": image_path.name,
            "easyocr_row": easyocr_row,
            "paddleocr_row": paddleocr_row
        }
    
    def run_comparison_test(self) -> Dict[str, Any]:
        """运行完整对比测试"""
        print(f"\n=== 开始OCR方法对比测试 ===")
        
        # 获取测试图片
        if not self.test_images_dir.exists():
            print(f"⚠️ 测试图片目录不存在: {self.test_images_dir}")
            return {"error": "测试目录不存在"}
        
        image_files = list(self.test_images_dir.glob("*.png"))
        image_files.extend(self.test_images_dir.glob("*.jpg"))
        image_files.sort()
        
        if not image_files:
            print("没有找到测试图片")
            return {"error": "没有测试图片"}
        
        print(f"找到 {len(image_files)} 张测试图片")
        
        # 为Excel收集数据
        easyocr_data = []
        paddleocr_data = []
        
        for i, image_path in enumerate(image_files, 1):
            print(f"\n进度: {i}/{len(image_files)}")
            result = self.test_single_image(image_path)
            
            if result.get("success"):
                easyocr_data.append(result["easyocr_row"])
                paddleocr_data.append(result["paddleocr_row"])
        
        return {
            "success": True,
            "total_images": len(image_files),
            "successful_tests": len(easyocr_data),
            "easyocr_data": easyocr_data,
            "paddleocr_data": paddleocr_data,
            "method_names": [name for name, _ in self._get_preprocessing_methods()]
        }
    
    def generate_comparison_report(self, test_results: Dict[str, Any]):
        """生成对比测试报告和Excel文件"""
        if not test_results.get("success"):
            print(f"测试失败: {test_results.get('error', '未知错误')}")
            return
        
        print(f"\n=== OCR方法对比报告 ===")
        
        total_images = test_results["successful_tests"]
        easyocr_data = test_results["easyocr_data"]
        paddleocr_data = test_results["paddleocr_data"]
        method_names = test_results["method_names"]
        
        print(f"测试图片总数: {total_images}")
        print(f"成功测试: {test_results['successful_tests']}")
        
        # 生成Excel文件
        if PANDAS_AVAILABLE and easyocr_data and paddleocr_data:
            try:
                self._generate_excel_report(easyocr_data, paddleocr_data, method_names)
                print(f"\n✓ Excel报告已生成: ocr_comparison_results.xlsx")
            except Exception as e:
                print(f"\n✗ Excel报告生成失败: {e}")
        
        # 生成简要统计
        self._generate_summary_statistics(easyocr_data, paddleocr_data, method_names)
        
        # 保存JSON备份
        try:
            import json
            with open("ocr_comparison_results.json", "w", encoding="utf-8") as f:
                json.dump(test_results, f, ensure_ascii=False, indent=2)
            print(f"\n详细结果JSON备份已保存到: ocr_comparison_results.json")
        except Exception as e:
            print(f"\n保存JSON备份失败: {e}")
    
    def _generate_excel_report(self, easyocr_data: List[Dict], paddleocr_data: List[Dict], method_names: List[str]):
        """生成Excel报告文件"""
        with pd.ExcelWriter('ocr_comparison_results.xlsx', engine='openpyxl') as writer:
            
            # 创建EasyOCR结果工作表
            if easyocr_data:
                easyocr_df = pd.DataFrame(easyocr_data)
                # 确保列的顺序：图片名称在前，然后是各种方法
                columns_order = ["图片名称"] + method_names
                easyocr_df = easyocr_df.reindex(columns=columns_order)
                easyocr_df.to_excel(writer, sheet_name='EasyOCR结果', index=False)
            
            # 创建PaddleOCR结果工作表
            if paddleocr_data:
                paddleocr_df = pd.DataFrame(paddleocr_data)
                # 确保列的顺序：图片名称在前，然后是各种方法
                columns_order = ["图片名称"] + method_names
                paddleocr_df = paddleocr_df.reindex(columns=columns_order)
                paddleocr_df.to_excel(writer, sheet_name='PaddleOCR结果', index=False)
            
            # 创建对比汇总工作表
            summary_data = self._create_summary_data(easyocr_data, paddleocr_data, method_names)
            if summary_data:
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='对比汇总', index=False)
    
    def _create_summary_data(self, easyocr_data: List[Dict], paddleocr_data: List[Dict], method_names: List[str]) -> List[Dict]:
        """创建汇总统计数据"""
        summary = []
        total_images = len(easyocr_data)
        
        for method in method_names:
            # 统计EasyOCR成功率
            easyocr_success = sum(1 for row in easyocr_data 
                                if row.get(method, "识别失败") not in ["识别失败", "处理失败"])
            easyocr_rate = easyocr_success / total_images * 100 if total_images > 0 else 0
            
            # 统计PaddleOCR成功率
            paddleocr_success = sum(1 for row in paddleocr_data 
                                  if row.get(method, "识别失败") not in ["识别失败", "处理失败"])
            paddleocr_rate = paddleocr_success / total_images * 100 if total_images > 0 else 0
            
            # 统计一致性（两个引擎结果相同）
            consistent_count = sum(1 for i in range(len(easyocr_data))
                                 if (easyocr_data[i].get(method, "") == paddleocr_data[i].get(method, "")
                                     and easyocr_data[i].get(method, "") not in ["识别失败", "处理失败"]))
            consistent_rate = consistent_count / total_images * 100 if total_images > 0 else 0
            
            summary.append({
                "预处理方法": method,
                "EasyOCR成功数": easyocr_success,
                "EasyOCR成功率": f"{easyocr_rate:.1f}%",
                "PaddleOCR成功数": paddleocr_success,
                "PaddleOCR成功率": f"{paddleocr_rate:.1f}%",
                "结果一致数": consistent_count,
                "结果一致率": f"{consistent_rate:.1f}%",
                "总图片数": total_images,
                "最佳成功率": max(easyocr_rate, paddleocr_rate)  # 用于排序
            })
        
        # 按最佳成功率排序
        summary.sort(key=lambda x: x["最佳成功率"], reverse=True)
        
        # 移除排序用的字段
        for item in summary:
            del item["最佳成功率"]
        
        return summary
    
    def _generate_summary_statistics(self, easyocr_data: List[Dict], paddleocr_data: List[Dict], method_names: List[str]):
        """生成简要统计信息"""
        print(f"\n=== 方法性能排名 ===")
        
        total_images = len(easyocr_data)
        method_scores = []
        
        for method in method_names:
            # 计算EasyOCR成功率
            easyocr_success = sum(1 for row in easyocr_data 
                                if row.get(method, "识别失败") not in ["识别失败", "处理失败"])
            easyocr_rate = easyocr_success / total_images * 100 if total_images > 0 else 0
            
            # 计算PaddleOCR成功率
            paddleocr_success = sum(1 for row in paddleocr_data 
                                  if row.get(method, "识别失败") not in ["识别失败", "处理失败"])
            paddleocr_rate = paddleocr_success / total_images * 100 if total_images > 0 else 0
            
            # 综合评分（取两者最高值）
            best_rate = max(easyocr_rate, paddleocr_rate)
            best_engine = "EasyOCR" if easyocr_rate >= paddleocr_rate else "PaddleOCR"
            
            method_scores.append((method, best_engine, best_rate, easyocr_rate, paddleocr_rate))
        
        # 按综合评分排序
        method_scores.sort(key=lambda x: x[2], reverse=True)
        
        print(f"{'排名':<4} {'预处理方法':<15} {'最佳引擎':<12} {'最佳成功率':<10} {'EasyOCR':<10} {'PaddleOCR'}")
        print("-" * 80)
        
        for rank, (method, best_engine, best_rate, easy_rate, paddle_rate) in enumerate(method_scores, 1):
            print(f"{rank:<4} {method:<15} {best_engine:<12} {best_rate:.1f}%{'':<5} {easy_rate:.1f}%{'':<5} {paddle_rate:.1f}%")
        
        # 推荐最佳方法
        if method_scores:
            best_method, best_engine, best_rate, _, _ = method_scores[0]
            print(f"\n推荐最佳方案: {best_method} + {best_engine} (成功率: {best_rate:.1f}%)")
        
        print(f"\n详细结果请查看 Excel 文件: ocr_comparison_results.xlsx")


def main():
    """主函数"""
    print("=== OCR方法对比测试工具 ===")
    print("测试多种预处理方法和OCR引擎组合，找到最佳识别方案")
    print("输出Excel格式的详细对比结果")
    
    # 检查pandas依赖
    if not PANDAS_AVAILABLE:
        print("\n⚠️ 注意: 缺少pandas或openpyxl，无法生成Excel输出")
        print("建议安装: pip install pandas openpyxl")
        choice = input("是否继续运行（只输出控制台结果）? (y/n): ").lower().strip()
        if choice != 'y':
            return
    
    # 选择GPU模式和调试模式
    use_gpu = input("\n是否使用GPU加速? (y/n, 默认n): ").lower().strip() == 'y'
    debug_mode = input("是否启用调试模式? (y/n, 默认n): ").lower().strip() == 'y'
    
    # 创建测试器
    tester = OCRMethodComparison(use_gpu=use_gpu, debug_mode=debug_mode)
    
    # 检查是否有可用引擎
    if not tester.easyocr_reader and not tester.paddleocr_reader:
        print("\n⚠️ 没有可用的OCR引擎，请检查安装!")
        print("需要安装: pip install easyocr paddlepaddle paddleocr opencv-python")
        return
    
    # 显示测试方法信息
    methods = tester._get_preprocessing_methods()
    print(f"\n将测试 {len(methods)} 种预处理方法:")
    for i, (method_name, _) in enumerate(methods, 1):
        print(f"  {i:2d}. {method_name}")
    
    # 运行测试
    print("\n开始对比测试...")
    print("注意: 这个测试会比较耗时，因为要测试多种方法组合")
    
    test_results = tester.run_comparison_test()
    
    # 生成报告
    tester.generate_comparison_report(test_results)
    
    print("\n=== 测试完成 ===")
    input("按回车键退出...")


if __name__ == "__main__":
    main()