# -*- coding: utf-8 -*-
"""
PaddleOCR问题诊断脚本
专门用于测试PaddleOCR的识别功能和API兼容性
"""

import cv2
import numpy as np
from pathlib import Path

def test_paddleocr_api():
    """测试PaddleOCR的不同API调用方式"""
    print("=== PaddleOCR API测试 ===")
    
    # 初始化PaddleOCR
    try:
        print("正在初始化PaddleOCR...")
        import paddleocr
        
        # 尝试不同的初始化参数
        ocr = paddleocr.PaddleOCR(
            use_textline_orientation=True,  # 替代过时的use_angle_cls
            lang='ch'
        )
        print("✓ PaddleOCR初始化成功")
        
    except Exception as e:
        print(f"✗ PaddleOCR初始化失败: {e}")
        return
    
    # 测试图片目录
    test_dir = Path("debug_images_20250724_221758")
    if not test_dir.exists():
        print(f"⚠️ 测试目录不存在: {test_dir}")
        return
    
    # 获取第一张图片进行测试
    image_files = list(test_dir.glob("*.png"))
    if not image_files:
        print("没有找到PNG图片")
        return
    
    test_image = image_files[0]
    print(f"\n测试图片: {test_image.name}")
    
    # 读取图片
    image = cv2.imread(str(test_image))
    if image is None:
        print("图片读取失败")
        return
    
    print(f"图片尺寸: {image.shape}")
    
    # 测试不同的API调用方式
    methods = [
        ("方法1: ocr(image)", lambda: ocr.ocr(image)),
        ("方法2: ocr(文件路径)", lambda: ocr.ocr(str(test_image))),
    ]
    
    for method_name, method_func in methods:
        print(f"\n--- {method_name} ---")
        try:
            results = method_func()
            print(f"返回结果类型: {type(results)}")
            
            if results:
                print(f"结果长度: {len(results)}")
                
                # 分析结果结构
                if isinstance(results, list) and len(results) > 0:
                    first_item = results[0]
                    print(f"第一个元素类型: {type(first_item)}")
                    
                    if isinstance(first_item, list) and len(first_item) > 0:
                        print(f"嵌套列表，内部元素数量: {len(first_item)}")
                        if len(first_item) > 0:
                            print(f"内部第一个元素类型: {type(first_item[0])}")
                            print(f"内部第一个元素内容: {first_item[0]}")
                    else:
                        print(f"直接列表，第一个元素内容: {first_item}")
                
                # 尝试提取文本
                texts = extract_texts_from_results(results)
                print(f"提取到的文本: {texts}")
            else:
                print("结果为空")
                
        except Exception as e:
            print(f"调用失败: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")

def extract_texts_from_results(results):
    """从PaddleOCR结果中提取文本 - 适配新的OCRResult格式"""
    texts = []
    
    if not results:
        return texts
    
    try:
        # 处理新的OCRResult格式
        for result in results:
            # 检查是否是OCRResult对象
            if hasattr(result, '__dict__') or isinstance(result, dict):
                # 尝试从字典或对象中提取rec_texts
                rec_texts = None
                
                if isinstance(result, dict):
                    rec_texts = result.get('rec_texts', [])
                else:
                    # 对象属性访问
                    rec_texts = getattr(result, 'rec_texts', [])
                
                # 添加识别到的文本
                if rec_texts:
                    for text in rec_texts:
                        if text and text.strip():
                            texts.append(text.strip())
                            
    except Exception as e:
        print(f"文本提取失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
    
    return texts

def test_simple_image():
    """测试简单的图片识别"""
    print("\n=== 简单图片测试 ===")
    
    # 创建一个简单的测试图片（白底黑字）
    import numpy as np
    
    # 创建白色背景图片
    test_img = np.ones((100, 300, 3), dtype=np.uint8) * 255
    
    # 使用OpenCV添加文本
    cv2.putText(test_img, "-1.23%", (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    
    # 保存测试图片
    cv2.imwrite("test_simple.png", test_img)
    print("已创建简单测试图片: test_simple.png")
    
    # 用PaddleOCR识别
    try:
        import paddleocr
        ocr = paddleocr.PaddleOCR(use_textline_orientation=True, lang='ch')
        
        results = ocr.ocr("test_simple.png")  # 移除cls参数
        print(f"识别结果: {results}")
        
        texts = extract_texts_from_results(results)
        print(f"提取文本: {texts}")
        
    except Exception as e:
        print(f"简单图片测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    test_paddleocr_api()
    test_simple_image()
    print("\n=== 测试完成 ===")
    input("按回车键退出...")