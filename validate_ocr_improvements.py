#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCR改进验证脚本
用于验证OCR错误修正的改进效果
"""

import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_specific_error_cases():
    """测试您提到的具体错误案例"""
    print("=== 具体错误案例验证 ===")
    
    from data_processor import parse_fund_value, fix_percentage_ocr_errors
    
    # 您提到的具体错误案例
    error_cases = [
        {
            'file': '5.png',
            'actual_content': '0.258%',
            'expected_output': 0.258,
            'original_output': 5.000,
            'test_inputs': ['0.258%', '0.2589', '5.000', '5000', '258']
        },
        {
            'file': '加115.png', 
            'actual_content': '-0.115%',
            'expected_output': -0.115,
            'original_output': 0.115,
            'test_inputs': ['-0.115%', '0.115', '115', '-115', '0115']
        },
        {
            'file': '98000.png',
            'actual_content': '-0.980%', 
            'expected_output': -0.980,
            'original_output': 98.000,
            'test_inputs': ['-0.980%', '98000', '98.000', '980', '-980']
        }
    ]
    
    total_tests = 0
    passed_tests = 0
    
    for case in error_cases:
        print(f"\n文件: {case['file']}")
        print(f"实际内容: {case['actual_content']}")
        print(f"期望输出: {case['expected_output']}")
        print(f"原始错误输出: {case['original_output']}")
        print("测试结果:")
        
        best_result = None
        best_error = float('inf')
        
        for test_input in case['test_inputs']:
            total_tests += 1
            
            # 测试错误修正
            corrected = fix_percentage_ocr_errors(test_input)
            
            # 测试解析
            parsed = parse_fund_value(test_input)
            
            # 计算误差
            error = abs(parsed - case['expected_output'])
            
            # 判断是否通过
            passed = error < 0.01
            if passed:
                passed_tests += 1
            
            # 记录最佳结果
            if error < best_error:
                best_error = error
                best_result = (test_input, corrected, parsed)
            
            status = "✓" if passed else "✗"
            print(f"  {test_input:10} -> {corrected:10} -> {parsed:8.3f} (误差: {error:.3f}) {status}")
        
        print(f"最佳结果: {best_result[0]} -> {best_result[2]:.3f} (误差: {best_error:.3f})")
    
    accuracy = passed_tests / total_tests * 100 if total_tests > 0 else 0
    print(f"\n总体准确率: {passed_tests}/{total_tests} = {accuracy:.1f}%")
    
    return accuracy


def test_context_awareness():
    """测试上下文感知功能"""
    print("\n=== 上下文感知功能验证 ===")
    
    from data_processor import parse_fund_value
    
    # 模拟真实的OCR识别场景
    scenarios = [
        {
            'name': '正常股票数据场景',
            'context': [0.258, -0.115, -0.980],
            'test_cases': [
                ('5000', 0.258, '大数值应该被修正为小数值'),
                ('115', -0.115, '正数应该被修正为负数'),
                ('98000', -0.980, '超大数值应该被修正为负小数'),
            ]
        },
        {
            'name': '高波动场景',
            'context': [5.23, -3.45, 2.18],
            'test_cases': [
                ('523', 5.23, '中等数值转换'),
                ('345', -3.45, '负数恢复'),
                ('218', 2.18, '正数保持'),
            ]
        }
    ]
    
    total_improvements = 0
    total_tests = 0
    
    for scenario in scenarios:
        print(f"\n场景: {scenario['name']}")
        print(f"上下文: {scenario['context']}")
        
        for test_input, expected, description in scenario['test_cases']:
            total_tests += 1
            
            # 无上下文解析
            no_context = parse_fund_value(test_input)
            
            # 有上下文解析
            with_context = parse_fund_value(test_input, scenario['context'])
            
            # 计算改进程度
            error_no_context = abs(no_context - expected)
            error_with_context = abs(with_context - expected)
            
            improved = error_with_context < error_no_context
            if improved:
                total_improvements += 1
            
            improvement_status = "改进" if improved else "无变化"
            print(f"  {test_input:8} -> 无上下文: {no_context:6.3f} | 有上下文: {with_context:6.3f} | 期望: {expected:6.3f} | {improvement_status}")
            print(f"    {description}")
    
    improvement_rate = total_improvements / total_tests * 100 if total_tests > 0 else 0
    print(f"\n上下文改进率: {total_improvements}/{total_tests} = {improvement_rate:.1f}%")
    
    return improvement_rate


def test_preprocessing_strategies():
    """测试预处理策略效果"""
    print("\n=== 预处理策略验证 ===")
    
    try:
        from image_processor import ImageProcessor
        import cv2
        import numpy as np
        
        # 创建模拟的小尺寸图像
        test_image = np.ones((36, 61, 3), dtype=np.uint8) * 255  # 白色背景
        
        processor = ImageProcessor(debug_mode=True, save_debug_images=False)
        
        strategies = [
            ('原始图像', lambda img: cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)),
            ('百分比专用v1', processor.preprocess_for_percentage),
            ('百分比专用v2', processor.preprocess_for_percentage_v2),
            ('百分比专用v3', processor.preprocess_for_percentage_v3),
            ('超保守策略v4', processor.preprocess_for_percentage_v4_conservative),
            ('超小区域专用', processor.preprocess_for_micro_region),
        ]
        
        print("预处理策略测试:")
        for name, strategy in strategies:
            try:
                result = strategy(test_image)
                height, width = result.shape[:2]
                print(f"  {name:15} -> 输出尺寸: {width}x{height}")
            except Exception as e:
                print(f"  {name:15} -> 错误: {str(e)}")
        
        print("建议使用超保守策略v4来保护小字符细节")
        
    except ImportError as e:
        print(f"无法导入图像处理模块: {e}")
        print("请确保OpenCV和相关依赖已正确安装")


def generate_improvement_report():
    """生成改进报告"""
    print("\n=== OCR改进效果报告 ===")
    
    # 运行所有测试
    accuracy = test_specific_error_cases()
    improvement_rate = test_context_awareness()
    test_preprocessing_strategies()
    
    # 生成报告
    timestamp = datetime.now().isoformat(timespec='seconds')
    
    report = f"""
OCR改进效果报告
生成时间: {timestamp}

1. 具体错误案例修正准确率: {accuracy:.1f}%
2. 上下文感知改进率: {improvement_rate:.1f}%
3. 预处理策略: 已优化，增加超保守策略

主要改进:
- 智能OCR错误修正算法
- 上下文感知的数值解析
- 保护小字符的预处理策略
- 多轮解析机制

建议:
- 继续收集错误案例进行优化
- 监控实际使用中的准确率
- 考虑使用机器学习方法进一步改进
"""
    
    print(report)
    
    # 保存报告到文件
    with open(f'ocr_improvement_report_{timestamp.replace(":", "-")}.txt', 'w', encoding='utf-8') as f:
        f.write(report)
    
    return {
        'accuracy': accuracy,
        'improvement_rate': improvement_rate,
        'timestamp': timestamp
    }


def main():
    """主函数"""
    print("OCR改进验证脚本")
    print("=" * 50)
    
    try:
        # 生成完整的改进报告
        results = generate_improvement_report()
        
        print(f"\n验证完成!")
        print(f"详细报告已保存到文件")
        
        # 返回结果供其他脚本使用
        return results
        
    except Exception as e:
        print(f"验证过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    main()
