# -*- coding: utf-8 -*-
"""
验证"开始分析"功能
确保简化PaddleOCR能够正常支持股票分析流程
"""

import logging
import sys
import traceback

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_compass_automator():
    """测试指南针自动化器"""
    print("=== 测试指南针自动化器 ===")
    
    try:
        from compass_automator import CompassAutomator
        
        # 创建指南针自动化器
        automator = CompassAutomator()
        print("✅ 指南针自动化器创建成功")
        
        # 检查数据提取器
        if hasattr(automator, 'data_extractor'):
            print("✅ 数据提取器已初始化")
        else:
            print("⚠️ 数据提取器未初始化")
        
        return True
        
    except Exception as e:
        print(f"❌ 指南针自动化器测试失败: {e}")
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_compass_data_extractor_ocr():
    """测试指南针数据提取器的OCR功能"""
    print("\n=== 测试指南针数据提取器OCR功能 ===")
    
    try:
        from compass_data_extractor import CompassDataExtractor
        from simple_ocr_manager import get_global_ocr_manager
        
        # 创建数据提取器
        extractor = CompassDataExtractor()
        print("✅ 数据提取器创建成功")
        
        # 初始化OCR引擎
        ocr_manager = get_global_ocr_manager()
        success = ocr_manager.initialize_ocr(use_gpu=False, debug_mode=True)
        
        if not success:
            print("❌ OCR引擎初始化失败")
            return False
        
        print("✅ OCR引擎初始化成功")
        
        # 获取OCR引擎
        ocr_engine = ocr_manager.get_ocr_engine()
        if ocr_engine is None:
            print("❌ 无法获取OCR引擎")
            return False
        
        print("✅ OCR引擎获取成功")
        
        # 测试OCR引擎的关键方法
        methods_to_test = [
            'test_fund_data_ocr_recognition_optimized',
            'test_fund_data_ocr_recognition',
            'test_raw_ocr_recognition'
        ]
        
        for method_name in methods_to_test:
            if hasattr(ocr_engine, method_name):
                print(f"✅ {method_name} 方法可用")
                
                # 尝试调用方法（使用模拟坐标）
                try:
                    method = getattr(ocr_engine, method_name)
                    result = method(100, 100, 200, 50)
                    
                    if isinstance(result, dict) and 'success' in result:
                        print(f"  └─ 方法调用成功，返回格式正确")
                    else:
                        print(f"  └─ 方法调用成功，但返回格式异常: {type(result)}")
                        
                except Exception as e:
                    print(f"  └─ 方法调用失败: {e}")
            else:
                print(f"❌ {method_name} 方法不可用")
                return False
        
        print("✅ 指南针数据提取器OCR功能测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 指南针数据提取器OCR功能测试失败: {e}")
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_gui_analysis_components():
    """测试GUI分析组件"""
    print("\n=== 测试GUI分析组件 ===")
    
    try:
        # 测试GUI分析模块导入
        from gui_analysis import GUIAnalysisMixin
        print("✅ GUI分析模块导入成功")
        
        # 测试数据处理器
        from data_processor import DataProcessor
        processor = DataProcessor()
        print("✅ 数据处理器创建成功")
        
        # 测试OCR管理器导入
        from gui_main import get_global_ocr_manager
        ocr_manager = get_global_ocr_manager()
        print("✅ OCR管理器导入成功")
        
        # 初始化OCR
        success = ocr_manager.initialize_ocr(use_gpu=False, debug_mode=True)
        if success:
            print("✅ OCR管理器初始化成功")
        else:
            print("❌ OCR管理器初始化失败")
            return False
        
        print("✅ GUI分析组件测试成功")
        return True
        
    except Exception as e:
        print(f"❌ GUI分析组件测试失败: {e}")
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_analysis_workflow_simulation():
    """模拟分析工作流程"""
    print("\n=== 模拟分析工作流程 ===")
    
    try:
        from compass_automator import CompassAutomator
        from simple_ocr_manager import get_global_ocr_manager
        
        # 创建指南针自动化器
        automator = CompassAutomator()
        print("✅ 指南针自动化器创建成功")
        
        # 初始化OCR
        ocr_manager = get_global_ocr_manager()
        success = ocr_manager.initialize_ocr(use_gpu=False, debug_mode=True)
        
        if not success:
            print("❌ OCR初始化失败")
            return False
        
        print("✅ OCR初始化成功")
        
        # 模拟分析单只股票的流程
        test_stock_code = "000001"
        print(f"模拟分析股票: {test_stock_code}")
        
        # 测试analyze_single_stock方法
        if hasattr(automator, 'analyze_single_stock'):
            print("✅ analyze_single_stock 方法可用")
            
            # 注意：这里不实际调用，因为需要指南针软件运行
            print("  └─ 方法签名验证通过（需要指南针软件运行才能实际测试）")
        else:
            print("❌ analyze_single_stock 方法不可用")
            return False
        
        # 测试get_fund_flow_data方法
        if hasattr(automator, 'get_fund_flow_data'):
            print("✅ get_fund_flow_data 方法可用")
        else:
            print("❌ get_fund_flow_data 方法不可用")
            return False
        
        print("✅ 分析工作流程模拟成功")
        return True
        
    except Exception as e:
        print(f"❌ 分析工作流程模拟失败: {e}")
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_ocr_performance():
    """测试OCR性能"""
    print("\n=== 测试OCR性能 ===")
    
    try:
        from simple_ocr_manager import get_global_ocr_manager
        import time
        
        # 获取OCR管理器
        ocr_manager = get_global_ocr_manager()
        ocr_manager.initialize_ocr(use_gpu=False, debug_mode=False)  # 关闭调试模式以测试性能
        
        ocr_engine = ocr_manager.get_ocr_engine()
        if ocr_engine is None:
            print("❌ 无法获取OCR引擎")
            return False
        
        # 测试多次OCR调用的性能
        test_count = 5
        total_time = 0
        
        print(f"进行{test_count}次OCR性能测试...")
        
        for i in range(test_count):
            start_time = time.time()
            
            # 调用OCR识别
            result = ocr_engine.test_fund_data_ocr_recognition_optimized(100, 100, 200, 50)
            
            end_time = time.time()
            execution_time = end_time - start_time
            total_time += execution_time
            
            print(f"  第{i+1}次: {execution_time:.3f}s, 成功: {result.get('success', False)}")
        
        average_time = total_time / test_count
        print(f"平均执行时间: {average_time:.3f}s")
        
        if average_time < 5.0:  # 期望每次OCR调用在5秒内完成
            print("✅ OCR性能测试通过")
            return True
        else:
            print("⚠️ OCR性能较慢，但功能正常")
            return True
        
    except Exception as e:
        print(f"❌ OCR性能测试失败: {e}")
        print(f"详细错误: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    print("开始验证'开始分析'功能...")
    print("确保简化PaddleOCR能够正常支持股票分析流程\n")
    
    # 测试各个组件
    tests = [
        ("指南针自动化器", test_compass_automator),
        ("指南针数据提取器OCR功能", test_compass_data_extractor_ocr),
        ("GUI分析组件", test_gui_analysis_components),
        ("分析工作流程模拟", test_analysis_workflow_simulation),
        ("OCR性能", test_ocr_performance)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n=== 验证结果汇总 ===")
    success_count = 0
    for test_name, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            success_count += 1
    
    print(f"\n总计: {success_count}/{len(results)} 个测试通过")
    
    if success_count == len(results):
        print("\n🎉 所有验证测试通过！")
        print("✅ 简化PaddleOCR已成功替换原有OCR实现")
        print("✅ '开始分析'功能应该能够正常使用简化PaddleOCR进行股票分析")
        print("✅ 系统已准备就绪，可以开始使用")
    elif success_count >= len(results) * 0.8:  # 80%以上通过
        print("\n✅ 大部分验证测试通过！")
        print("✅ 简化PaddleOCR基本可用")
        print("⚠️ 部分功能可能需要进一步调试")
    else:
        print("\n⚠️ 验证测试未完全通过")
        print("❌ 可能存在兼容性问题，请检查错误信息")
    
    print("\n📝 使用说明：")
    print("1. 确保指南针软件已安装并运行")
    print("2. 在GUI中点击'开始分析'按钮")
    print("3. 系统将自动使用简化PaddleOCR进行OCR识别")
    print("4. 查看日志输出确认OCR引擎工作状态")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
