# -*- coding: utf-8 -*-
"""
GUI界面设置模块
包含所有界面初始化和布局相关的方法
"""

import tkinter as tk
from tkinter import ttk, scrolledtext
import logging
import sys

from config import GUI_CONFIG, APP_CONFIG
from gui_handlers import GUILogHandler


class GUISetupMixin:
    """GUI界面设置Mixin类"""
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=getattr(logging, APP_CONFIG['log_level']),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_window(self):
        """设置主窗口"""
        config = GUI_CONFIG['main_window']
        self.root.title(config['title'])
        self.root.geometry(f"{config['width']}x{config['height']}")
        self.root.minsize(config['min_width'], config['min_height'])
        
        # 设置窗口居中
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (config['width'] // 2)
        y = (self.root.winfo_screenheight() // 2) - (config['height'] // 2)
        self.root.geometry(f"{config['width']}x{config['height']}+{x}+{y}")
        
    def setup_variables(self):
        """设置变量"""
        self.excel_path_var = tk.StringVar()
        self.status_var = tk.StringVar(value=GUI_CONFIG['status_bar']['ready_message'])
        self.progress_var = tk.DoubleVar()
        self.region_info_var = tk.StringVar(value="区域位置与尺寸")
        
    def setup_widgets(self):
        """设置控件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 1. 文件路径区域 - 单行布局
        file_frame = ttk.LabelFrame(main_frame, text="文件路径", padding="10")
        file_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.grid_columnconfigure(0, weight=1)  # 文件路径框可扩展
        
        # 文件路径输入框和浏览按钮在同一行
        path_container = ttk.Frame(file_frame)
        path_container.grid(row=0, column=0, sticky=(tk.W, tk.E))
        path_container.grid_columnconfigure(0, weight=1)
        
        self.excel_entry = ttk.Entry(path_container, textvariable=self.excel_path_var, font=("Microsoft YaHei", 9))
        self.excel_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 10))
        
        ttk.Button(path_container, text="浏览", command=self.select_excel_file).grid(row=0, column=1)
        
        # 2. OCR区域设置
        ocr_frame = ttk.LabelFrame(main_frame, text="OCR区域设置", padding="10")
        ocr_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # OCR设置容器
        ocr_container = ttk.Frame(ocr_frame)
        ocr_container.grid(row=0, column=0, sticky=(tk.W, tk.E))
        ocr_container.grid_columnconfigure(1, weight=1)
        
        # 选择屏幕区域按钮
        ttk.Button(ocr_container, text="选择屏幕区域", command=self.select_screen_region).grid(
            row=0, column=0, padx=(0, 20)
        )
        
        # 区域位置与尺寸显示
        self.region_label = ttk.Label(ocr_container, textvariable=self.region_info_var, font=("Microsoft YaHei", 9))
        self.region_label.grid(row=0, column=1, sticky=tk.W)
        
        # OCR状态显示
        self.ocr_status_label = ttk.Label(ocr_frame, text="OCR状态：", foreground="gray", font=("Microsoft YaHei", 9))
        self.ocr_status_label.grid(row=1, column=0, sticky="w", pady=(10, 0))
        
        # 3. 操作控制区域 - 按钮和进度条在同一个框架内
        control_frame = ttk.LabelFrame(main_frame, text="", padding="10")
        control_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        control_frame.grid_columnconfigure(4, weight=1)  # 进度条列可扩展，保持标签位置固定
        
        # 操作按钮
        self.start_btn = ttk.Button(control_frame, text="开始分析", command=self.start_analysis)
        self.start_btn.grid(row=0, column=0, padx=(0, 10))
        
        self.stop_btn = ttk.Button(control_frame, text="停止分析", command=self.stop_analysis, state=tk.DISABLED)
        self.stop_btn.grid(row=0, column=1, padx=(0, 10))
        
        self.save_btn = ttk.Button(control_frame, text="保存结果", command=self.save_results, state=tk.DISABLED)
        self.save_btn.grid(row=0, column=2, padx=(0, 20))
        
        # 处理进度 - 标签与进度条间距固定
        ttk.Label(control_frame, text="处理进度：", font=("Microsoft YaHei", 9)).grid(row=0, column=3, sticky=tk.W, padx=(0, 10))
        self.progress_bar = ttk.Progressbar(control_frame, variable=self.progress_var, maximum=100, length=200)
        self.progress_bar.grid(row=0, column=4, sticky=(tk.W, tk.E))
        
        # 4. 筛选结果区域
        result_frame = ttk.LabelFrame(main_frame, text="筛选结果", padding="10")
        result_frame.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 结果表格
        self.setup_result_table(result_frame)
        
        # 5. 运行日志区域
        log_frame = ttk.LabelFrame(main_frame, text="运行日志", padding="10")
        log_frame.grid(row=4, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, height=12, width=80, font=("Consolas", 9))
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置日志框架权重
        log_frame.grid_rowconfigure(0, weight=1)
        log_frame.grid_columnconfigure(0, weight=1)
        
        # 6. 状态栏
        self.status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W, 
                                   font=("Microsoft YaHei", 9), padding="5")
        self.status_bar.grid(row=5, column=0, sticky=(tk.W, tk.E), pady=(5, 0))
        
        # 初始化日志处理器
        self.log_handler = GUILogHandler(self.log_text)
        self.logger.addHandler(self.log_handler)
        
    def setup_result_table(self, parent):
        """设置结果表格"""
        # 结果表格 - 增加默认显示行数确保内容可见
        columns = GUI_CONFIG['table']['columns']
        self.result_tree = ttk.Treeview(parent, columns=columns, show='headings', height=12)
        
        # 设置列标题和宽度
        for i, col in enumerate(columns):
            self.result_tree.heading(col, text=col)
            width = GUI_CONFIG['table']['column_widths'][i]
            self.result_tree.column(col, width=width, anchor=tk.CENTER)
        
        # 添加滚动条
        scrollbar_v = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.result_tree.yview)
        scrollbar_h = ttk.Scrollbar(parent, orient=tk.HORIZONTAL, command=self.result_tree.xview)
        self.result_tree.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)
        
        # 布局
        self.result_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar_v.grid(row=0, column=1, sticky=(tk.N, tk.S))
        scrollbar_h.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        # 配置权重
        parent.grid_rowconfigure(0, weight=1)
        parent.grid_columnconfigure(0, weight=1)
        
    def setup_layout(self):
        """设置布局权重"""
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)
        
        # 主框架布局 - 单列布局
        main_frame = self.root.grid_slaves()[0]
        main_frame.grid_rowconfigure(3, weight=4)  # 筛选结果区域 - 占更多空间
        main_frame.grid_rowconfigure(4, weight=2)  # 运行日志区域
        main_frame.grid_columnconfigure(0, weight=1)  # 单列可扩展