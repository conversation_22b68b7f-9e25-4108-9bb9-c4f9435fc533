# -*- coding: utf-8 -*-
"""
智能等待机制模块
根据界面状态动态调整等待时间，替代固定等待
"""

import time
import logging
from typing import Optional, Callable, Any, Tuple
from config import APP_CONFIG


class SmartWaiter:
    """智能等待器"""
    
    def __init__(self):
        """初始化智能等待器"""
        self.logger = logging.getLogger(__name__)
        
        # 等待配置
        self.min_wait = 0.1  # 最小等待时间（秒）
        self.max_wait = APP_CONFIG.get('page_load_wait', 3)  # 最大等待时间
        self.check_interval = 0.1  # 检查间隔
        
        # 统计信息
        self.wait_times = []  # 记录实际等待时间
        self.total_saved_time = 0.0  # 总共节省的时间
    
    def wait_for_condition(self, condition_func: Callable[[], bool], 
                          timeout: float = None, 
                          description: str = "条件满足") -> bool:
        """
        等待直到条件满足或超时
        
        Args:
            condition_func: 条件检查函数，返回True表示条件满足
            timeout: 超时时间（秒），默认使用配置的最大等待时间
            description: 等待描述，用于日志
            
        Returns:
            是否在超时前满足条件
        """
        if timeout is None:
            timeout = self.max_wait
            
        start_time = time.time()
        
        try:
            while time.time() - start_time < timeout:
                if condition_func():
                    actual_wait = time.time() - start_time
                    saved_time = max(0, timeout - actual_wait)
                    
                    self.wait_times.append(actual_wait)
                    self.total_saved_time += saved_time
                    
                    if saved_time > 0.5:  # 只在节省时间较多时记录
                        self.logger.debug(f"{description}满足，实际等待{actual_wait:.2f}s，节省{saved_time:.2f}s")
                    
                    return True
                
                time.sleep(self.check_interval)
            
            # 超时
            self.logger.warning(f"等待{description}超时({timeout:.2f}s)")
            return False
            
        except Exception as e:
            self.logger.error(f"等待{description}时发生错误: {str(e)}")
            return False
    
    def wait_with_fallback(self, primary_wait: float, 
                          condition_func: Optional[Callable[[], bool]] = None,
                          description: str = "操作完成") -> float:
        """
        带回退的智能等待：优先使用条件等待，失败时使用固定等待
        
        Args:
            primary_wait: 主要等待时间（作为超时时间）
            condition_func: 可选的条件检查函数
            description: 等待描述
            
        Returns:
            实际等待时间
        """
        start_time = time.time()
        
        if condition_func:
            if self.wait_for_condition(condition_func, primary_wait, description):
                return time.time() - start_time
            else:
                # 条件等待失败，使用固定等待的剩余时间
                elapsed = time.time() - start_time
                remaining = max(0, primary_wait - elapsed)
                if remaining > 0:
                    self.logger.debug(f"条件等待失败，继续固定等待{remaining:.2f}s")
                    time.sleep(remaining)
        else:
            # 没有条件函数，直接固定等待
            time.sleep(primary_wait)
        
        return time.time() - start_time
    
    def smart_page_load_wait(self, window, description: str = "页面加载") -> float:
        """
        智能页面加载等待
        
        Args:
            window: 窗口对象
            description: 页面描述
            
        Returns:
            实际等待时间
        """
        def page_loaded():
            try:
                # 检查窗口是否响应
                if not window or not window.exists():
                    return False
                
                # 可以添加更多的页面加载检查逻辑
                # 比如检查特定控件是否出现、窗口标题是否更新等
                return True
                
            except Exception:
                return False
        
        return self.wait_with_fallback(
            primary_wait=self.max_wait,
            condition_func=page_loaded,
            description=description
        )
    
    def smart_operation_wait(self, description: str = "操作") -> float:
        """
        智能操作等待（用于替代固定的操作等待）
        
        Args:
            description: 操作描述
            
        Returns:
            实际等待时间
        """
        # 对于一般操作，使用较短的固定等待
        base_wait = APP_CONFIG.get('wait_time', 2)
        optimized_wait = min(base_wait, 1.0)  # 优化后最多等待1秒
        
        start_time = time.time()
        time.sleep(optimized_wait)
        actual_wait = time.time() - start_time
        
        saved_time = base_wait - optimized_wait
        if saved_time > 0:
            self.total_saved_time += saved_time
            
        return actual_wait
    
    def get_statistics(self) -> dict:
        """
        获取等待统计信息
        
        Returns:
            统计信息字典
        """
        if not self.wait_times:
            return {
                'total_waits': 0,
                'total_time': 0.0,
                'average_wait': 0.0,
                'total_saved_time': self.total_saved_time
            }
        
        return {
            'total_waits': len(self.wait_times),
            'total_time': sum(self.wait_times),
            'average_wait': sum(self.wait_times) / len(self.wait_times),
            'min_wait': min(self.wait_times),
            'max_wait': max(self.wait_times),
            'total_saved_time': self.total_saved_time
        }
    
    def reset_statistics(self):
        """重置统计信息"""
        self.wait_times.clear()
        self.total_saved_time = 0.0
        self.logger.info("智能等待器统计信息已重置")


# 全局智能等待器实例
smart_waiter = SmartWaiter()


def get_smart_waiter() -> SmartWaiter:
    """
    获取全局智能等待器实例
    
    Returns:
        智能等待器实例
    """
    return smart_waiter