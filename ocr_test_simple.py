# -*- coding: utf-8 -*-
"""
简化版OCR测试页面
专门用于测试EasyOCR和PaddleOCR对图片的识别效果
"""

import os
import time
import logging
import warnings
from pathlib import Path
from typing import Dict, List, Any, Optional
import cv2
import numpy as np

# 抑制警告
warnings.filterwarnings("ignore")
os.environ['PYTORCH_DISABLE_PIN_MEMORY_WARNING'] = '1'

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleOCRTester:
    """简化版OCR测试器"""
    
    def __init__(self, use_gpu: bool = False):
        """
        初始化OCR测试器
        
        Args:
            use_gpu: 是否使用GPU加速
        """
        self.use_gpu = use_gpu
        self.easyocr_reader = None
        self.paddleocr_reader = None
        
        # 测试图片目录
        self.test_images_dir = Path("debug_images_20250724_221758")
        
        print(f"=== 简化版OCR测试器初始化 ===")
        print(f"GPU模式: {'启用' if use_gpu else '禁用'}")
        print(f"测试图片目录: {self.test_images_dir}")
        
        # 初始化OCR引擎
        self._init_ocr_engines()
    
    def _init_ocr_engines(self):
        """初始化OCR引擎"""
        print("\n--- 初始化OCR引擎 ---")
        
        # 初始化EasyOCR
        try:
            print("正在初始化EasyOCR...")
            import easyocr
            self.easyocr_reader = easyocr.Reader(['ch_sim', 'en'], gpu=self.use_gpu, verbose=False)
            print("✓ EasyOCR初始化成功")
        except Exception as e:
            print(f"✗ EasyOCR初始化失败: {e}")
            self.easyocr_reader = None
        
        # 初始化PaddleOCR
        try:
            print("正在初始化PaddleOCR...")
            import paddleocr
            self.paddleocr_reader = paddleocr.PaddleOCR(
                use_angle_cls=True, 
                lang='ch', 
                show_log=False,
                use_gpu=self.use_gpu
            )
            print("✓ PaddleOCR初始化成功")
        except Exception as e:
            print(f"✗ PaddleOCR初始化失败: {e}")
            self.paddleocr_reader = None
        
        # 检查可用引擎
        available_engines = []
        if self.easyocr_reader:
            available_engines.append("EasyOCR")
        if self.paddleocr_reader:
            available_engines.append("PaddleOCR")
        
        if available_engines:
            print(f"可用的OCR引擎: {', '.join(available_engines)}")
        else:
            print("⚠️ 没有可用的OCR引擎！")
    
    def _get_test_images(self) -> List[Path]:
        """获取测试图片列表"""
        if not self.test_images_dir.exists():
            print(f"⚠️ 测试图片目录不存在: {self.test_images_dir}")
            return []
        
        # 支持的图片格式
        image_extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.tiff']
        
        images = []
        for ext in image_extensions:
            images.extend(self.test_images_dir.glob(f"*{ext}"))
            images.extend(self.test_images_dir.glob(f"*{ext.upper()}"))
        
        # 按文件名排序
        images.sort(key=lambda x: x.name)
        
        print(f"找到 {len(images)} 张测试图片")
        return images
    
    def _recognize_with_easyocr(self, image_path: Path) -> Dict[str, Any]:
        """使用EasyOCR识别图片"""
        if not self.easyocr_reader:
            return {"error": "EasyOCR不可用"}
        
        try:
            # 读取图片
            image = cv2.imread(str(image_path))
            if image is None:
                return {"error": "图片读取失败"}
            
            # OCR识别
            start_time = time.time()
            results = self.easyocr_reader.readtext(image)
            end_time = time.time()
            
            # 提取文本
            texts = []
            for result in results:
                if len(result) >= 2:
                    bbox, text, confidence = result[0], result[1], result[2] if len(result) > 2 else 0.0
                    texts.append({
                        "text": text,
                        "confidence": confidence,
                        "bbox": bbox
                    })
            
            return {
                "success": True,
                "texts": texts,
                "time_used": round(end_time - start_time, 3),
                "total_results": len(texts)
            }
            
        except Exception as e:
            return {"error": f"EasyOCR识别失败: {e}"}
    
    def _recognize_with_paddleocr(self, image_path: Path) -> Dict[str, Any]:
        """使用PaddleOCR识别图片"""
        if not self.paddleocr_reader:
            return {"error": "PaddleOCR不可用"}
        
        try:
            # OCR识别
            start_time = time.time()
            results = self.paddleocr_reader.ocr(str(image_path), cls=True)
            end_time = time.time()
            
            # 处理结果
            texts = []
            if results and results[0]:  # PaddleOCR可能返回None
                for result in results[0]:
                    if len(result) >= 2:
                        bbox, (text, confidence) = result[0], result[1]
                        texts.append({
                            "text": text,
                            "confidence": confidence,
                            "bbox": bbox
                        })
            
            return {
                "success": True,
                "texts": texts,
                "time_used": round(end_time - start_time, 3),
                "total_results": len(texts)
            }
            
        except Exception as e:
            return {"error": f"PaddleOCR识别失败: {e}"}
    
    def test_single_image(self, image_path: Path) -> Dict[str, Any]:
        """测试单张图片"""
        print(f"\n--- 测试图片: {image_path.name} ---")
        
        results = {
            "image_name": image_path.name,
            "image_path": str(image_path),
            "easyocr": {},
            "paddleocr": {}
        }
        
        # EasyOCR识别
        if self.easyocr_reader:
            print("使用EasyOCR识别...")
            results["easyocr"] = self._recognize_with_easyocr(image_path)
            
            if results["easyocr"].get("success"):
                texts = [item["text"] for item in results["easyocr"]["texts"]]
                time_used = results["easyocr"]["time_used"]
                print(f"EasyOCR结果: {texts} (耗时: {time_used}s)")
            else:
                print(f"EasyOCR失败: {results['easyocr'].get('error', '未知错误')}")
        
        # PaddleOCR识别
        if self.paddleocr_reader:
            print("使用PaddleOCR识别...")
            results["paddleocr"] = self._recognize_with_paddleocr(image_path)
            
            if results["paddleocr"].get("success"):
                texts = [item["text"] for item in results["paddleocr"]["texts"]]
                time_used = results["paddleocr"]["time_used"]
                print(f"PaddleOCR结果: {texts} (耗时: {time_used}s)")
            else:
                print(f"PaddleOCR失败: {results['paddleocr'].get('error', '未知错误')}")
        
        return results
    
    def test_all_images(self) -> List[Dict[str, Any]]:
        """测试所有图片"""
        print(f"\n=== 开始批量测试 ===")
        
        # 获取测试图片
        images = self._get_test_images()
        if not images:
            print("没有找到测试图片")
            return []
        
        all_results = []
        
        for i, image_path in enumerate(images, 1):
            print(f"\n进度: {i}/{len(images)}")
            result = self.test_single_image(image_path)
            all_results.append(result)
        
        return all_results
    
    def generate_summary_report(self, all_results: List[Dict[str, Any]]):
        """生成汇总报告"""
        print(f"\n=== 测试汇总报告 ===")
        
        total_images = len(all_results)
        easyocr_success = 0
        paddleocr_success = 0
        easyocr_total_time = 0
        paddleocr_total_time = 0
        both_success = 0
        both_consistent = 0
        
        # 统计成功率
        for result in all_results:
            easyocr_ok = result["easyocr"].get("success", False)
            paddleocr_ok = result["paddleocr"].get("success", False)
            
            if easyocr_ok:
                easyocr_success += 1
                easyocr_total_time += result["easyocr"].get("time_used", 0)
            
            if paddleocr_ok:
                paddleocr_success += 1
                paddleocr_total_time += result["paddleocr"].get("time_used", 0)
            
            if easyocr_ok and paddleocr_ok:
                both_success += 1
                # 检查结果一致性
                easyocr_texts = [item["text"] for item in result["easyocr"]["texts"]]
                paddleocr_texts = [item["text"] for item in result["paddleocr"]["texts"]]
                if set(easyocr_texts) == set(paddleocr_texts):
                    both_consistent += 1
        
        print(f"测试图片总数: {total_images}")
        print(f"\n性能对比:")
        print(f"  EasyOCR:   成功率 {easyocr_success}/{total_images} ({easyocr_success/total_images*100:.1f}%), 平均耗时 {easyocr_total_time/max(easyocr_success, 1):.3f}s")
        print(f"  PaddleOCR: 成功率 {paddleocr_success}/{total_images} ({paddleocr_success/total_images*100:.1f}%), 平均耗时 {paddleocr_total_time/max(paddleocr_success, 1):.3f}s")
        print(f"  两者都成功: {both_success}/{total_images} ({both_success/total_images*100:.1f}%)")
        print(f"  结果一致: {both_consistent}/{both_success} ({both_consistent/max(both_success, 1)*100:.1f}%)")
        
        # 推荐最佳方法
        if easyocr_success > paddleocr_success:
            print(f"\n推荐: EasyOCR (更高成功率)")
        elif paddleocr_success > easyocr_success:
            print(f"\n推荐: PaddleOCR (更高成功率)")
        else:
            avg_time_easy = easyocr_total_time/max(easyocr_success, 1)
            avg_time_paddle = paddleocr_total_time/max(paddleocr_success, 1)
            if avg_time_easy < avg_time_paddle:
                print(f"\n推荐: EasyOCR (相同成功率，更快速度)")
            else:
                print(f"\n推荐: PaddleOCR (相同成功率，更快速度)")
        
        # 详细结果表格
        print(f"\n=== 详细识别结果 ===")
        print(f"{'图片名':<15} {'EasyOCR':<20} {'PaddleOCR':<20} {'状态'}")
        print("-" * 80)
        
        for result in all_results:
            image_name = result["image_name"][:14]  # 截断长文件名
            
            # EasyOCR结果
            easyocr_text = "失败"
            if result["easyocr"].get("success"):
                texts = [item["text"] for item in result["easyocr"]["texts"]]
                easyocr_text = str(texts)[:19] if texts else "空"
            
            # PaddleOCR结果
            paddleocr_text = "失败"
            if result["paddleocr"].get("success"):
                texts = [item["text"] for item in result["paddleocr"]["texts"]]
                paddleocr_text = str(texts)[:19] if texts else "空"
            
            # 状态
            if result["easyocr"].get("success") and result["paddleocr"].get("success"):
                easy_texts = [item["text"] for item in result["easyocr"]["texts"]]
                paddle_texts = [item["text"] for item in result["paddleocr"]["texts"]]
                status = "一致" if set(easy_texts) == set(paddle_texts) else "不一致"
            elif result["easyocr"].get("success") or result["paddleocr"].get("success"):
                status = "部分成功"
            else:
                status = "都失败"
            
            print(f"{image_name:<15} {easyocr_text:<20} {paddleocr_text:<20} {status}")
    
    def save_results_to_file(self, all_results: List[Dict[str, Any]]):
        """保存结果到文件"""
        try:
            import json
            with open("ocr_test_results.json", "w", encoding="utf-8") as f:
                json.dump(all_results, f, ensure_ascii=False, indent=2)
            print(f"\n结果已保存到: ocr_test_results.json")
        except Exception as e:
            print(f"\n保存结果失败: {e}")


def main():
    """主函数"""
    print("=== 简化版OCR测试工具 ===")
    print("专门用于测试debug_images_20250724_221758目录中图片的OCR识别效果")
    
    # 选择GPU模式
    use_gpu = input("\n是否使用GPU加速? (y/n, 默认n): ").lower().strip() == 'y'
    
    # 创建测试器
    tester = SimpleOCRTester(use_gpu=use_gpu)
    
    # 检查是否有可用引擎
    if not tester.easyocr_reader and not tester.paddleocr_reader:
        print("\n⚠️ 没有可用的OCR引擎，请检查安装!")
        print("需要安装: pip install easyocr paddlepaddle paddleocr opencv-python")
        return
    
    # 开始测试
    print("\n开始测试...")
    all_results = tester.test_all_images()
    
    if all_results:
        # 生成汇总报告
        tester.generate_summary_report(all_results)
        # 保存结果到文件
        tester.save_results_to_file(all_results)
    else:
        print("\n没有测试结果")
    
    print("\n=== 测试完成 ===")
    input("按回车键退出...")  # 防止窗口立即关闭


if __name__ == "__main__":
    main()