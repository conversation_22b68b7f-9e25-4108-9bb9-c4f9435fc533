# -*- coding: utf-8 -*-
"""
GUI显示模块
包含结果显示和界面更新相关功能
"""

import tkinter as tk
from tkinter import ttk
import tkinter.scrolledtext as scrolledtext


class GUIDisplayMixin:
    """GUI显示Mixin类"""
    
    def add_result_to_table(self, result):
        """添加结果到表格"""
        try:
            row_count = len(self.result_tree.get_children()) + 1
            values = [
                row_count,
                result['stock_code'],
                result.get('today_fund', 0),
                result.get('yesterday_fund', 0),
                result.get('day_before_yesterday_fund', 0),
                result.get('status', '')
            ]
            
            item = self.result_tree.insert('', 'end', values=values)
            
            # 根据状态设置颜色
            if result.get('status') == '符合条件':
                self.result_tree.item(item, tags=('success',))
            elif result.get('status') == '不符合条件':
                self.result_tree.item(item, tags=('fail',))
            else:
                self.result_tree.item(item, tags=('error',))
                
            # 配置标签颜色
            self.result_tree.tag_configure('success', background='lightgreen')
            self.result_tree.tag_configure('fail', background='lightcoral')
            self.result_tree.tag_configure('error', background='lightyellow')
            
            # 自动滚动到最新项
            self.result_tree.see(item)
            
        except Exception as e:
            self.logger.error(f"添加结果到表格失败: {str(e)}")
    
    def update_table_with_filtered_results(self, filtered_results):
        """批量更新表格，只显示符合条件的股票"""
        try:
            # 清空当前表格
            for item in self.result_tree.get_children():
                self.result_tree.delete(item)
            
            # 只添加符合条件的股票
            if filtered_results:
                for i, result in enumerate(filtered_results, 1):
                    values = [
                        i,  # 重新编号
                        result['stock_code'],
                        result.get('today_fund', 0),
                        result.get('yesterday_fund', 0),
                        result.get('day_before_yesterday_fund', 0),
                        result.get('status', '')
                    ]
                    
                    item = self.result_tree.insert('', 'end', values=values)
                    
                    # 符合条件的股票用绿色背景
                    self.result_tree.item(item, tags=('success',))
                
                # 配置标签颜色
                self.result_tree.tag_configure('success', background='lightgreen')
                
                self.logger.info(f"表格已更新，显示 {len(filtered_results)} 只符合条件的股票")
            else:
                self.logger.info("没有符合条件的股票")
                
        except Exception as e:
            self.logger.error(f"批量更新表格失败: {str(e)}")
    
    def _show_ocr_result_window(self, result_text: str):
        """显示OCR结果窗口"""
        try:
            # 创建新窗口
            result_window = tk.Toplevel(self.root)
            result_window.title("OCR识别结果")
            result_window.geometry("700x500")
            
            # 文本框显示结果
            text_frame = ttk.Frame(result_window)
            text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            result_display = scrolledtext.ScrolledText(text_frame, wrap=tk.WORD, font=("Microsoft YaHei", 10))
            result_display.pack(fill=tk.BOTH, expand=True)
            result_display.insert(tk.END, result_text)
            result_display.config(state=tk.DISABLED)
            
            # 按钮框架
            btn_frame = ttk.Frame(result_window)
            btn_frame.pack(fill=tk.X, padx=10, pady=5)
            
            # 关闭按钮
            close_btn = ttk.Button(btn_frame, text="关闭", command=result_window.destroy)
            close_btn.pack(side=tk.RIGHT)
            
        except Exception as e:
            self.logger.error(f"显示OCR结果窗口失败: {str(e)}")
            # 如果窗口创建失败，回退到简单消息框
            tk.messagebox.showinfo("OCR识别结果", result_text[:1000] + "..." if len(result_text) > 1000 else result_text)